"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 移除本地缓存管理，使用工具函数中的缓存\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            // 强制不使用缓存，确保获取最新数据\n            loadClasses(firstSchool.id, false);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        } else if (selectedSchool) {\n            // 如果已经有选中的学校，重新加载班级数据（不使用缓存）\n            console.log(\"班级管理：重新加载当前学校的班级数据\");\n            loadClasses(selectedSchool.id, false);\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表（强制不使用缓存，确保数据最新）\n        loadClasses(school.id, false);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表 - 使用工具函数\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            const schoolList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherSchools)();\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                loadClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(error.message || \"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用工具函数\n    const loadClasses = async function(schoolId) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            // 延迟重试，等待用户信息加载完成\n            setTimeout(()=>{\n                if (userInfo === null || userInfo === void 0 ? void 0 : userInfo.id) {\n                    console.log(\"用户信息已加载，重新尝试获取班级列表\");\n                    loadClasses(schoolId, useCache);\n                }\n            }, 1000);\n            return;\n        }\n        try {\n            // 通知父组件开始加载\n            onClassesUpdate && onClassesUpdate([], true, null);\n            console.log(\"开始获取班级列表，学校ID:\", schoolId, \"用户ID:\", userInfo.id);\n            const classList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherClasses)(schoolId, userInfo.id, useCache);\n            // 同时更新ClassSelectionModal的缓存\n            (0,_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__.updateClassCache)(schoolId, classList);\n            // 通知父组件数据更新\n            onClassesUpdate && onClassesUpdate(classList, false, null);\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            // 如果班级列表为空，尝试不使用缓存重新获取\n            if (classList.length === 0 && useCache) {\n                console.log(\"班级列表为空，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 500);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = error.message || \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            // 如果使用缓存失败，尝试不使用缓存重新获取\n            if (useCache) {\n                console.log(\"使用缓存获取失败，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 1000);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined),\n            userInfo.nickName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"login-prompt\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-icon-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-text\",\n                                children: \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-subtitle\",\n                                children: \"请先登录以使用完整功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_3__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});