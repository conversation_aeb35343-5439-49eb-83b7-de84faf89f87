"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classUtils.ts":
/*!*******************************************!*\
  !*** ./app/workbench/utils/classUtils.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSchoolsCache: function() { return /* binding */ clearSchoolsCache; },\n/* harmony export */   clearTeacherClassCache: function() { return /* binding */ clearTeacherClassCache; },\n/* harmony export */   deleteClass: function() { return /* binding */ deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* binding */ deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* binding */ editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* binding */ exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* binding */ exportStudentsViaAPI; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* binding */ fetchClassStudentsWithNotification; },\n/* harmony export */   fetchStudentPoints: function() { return /* binding */ fetchStudentPoints; },\n/* harmony export */   fetchTeacherClasses: function() { return /* binding */ fetchTeacherClasses; },\n/* harmony export */   fetchTeacherSchools: function() { return /* binding */ fetchTeacherSchools; },\n/* harmony export */   fetchUserSchools: function() { return /* binding */ fetchUserSchools; },\n/* harmony export */   generateClassInviteCode: function() { return /* binding */ generateClassInviteCode; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* binding */ getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* binding */ getMinAvailablePoints; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* binding */ getSchoolsCacheInfo; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* binding */ getTeacherClassCacheInfo; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* binding */ handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* binding */ handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* binding */ handleSchoolSelection; },\n/* harmony export */   initSchoolModalState: function() { return /* binding */ initSchoolModalState; },\n/* harmony export */   preloadSchoolsData: function() { return /* binding */ preloadSchoolsData; },\n/* harmony export */   removeAssistantTeacher: function() { return /* binding */ removeAssistantTeacher; },\n/* harmony export */   searchTeacherByPhone: function() { return /* binding */ searchTeacherByPhone; },\n/* harmony export */   transferClass: function() { return /* binding */ transferClass; },\n/* harmony export */   updateClassInfo: function() { return /* binding */ updateClassInfo; },\n/* harmony export */   updateTeacherClassCache: function() { return /* binding */ updateTeacherClassCache; },\n/* harmony export */   validateClassName: function() { return /* binding */ validateClassName; }\n/* harmony export */ });\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n\n\n\n\n\n/**\n * 班级管理相关工具函数\n */ /**\n * 编辑班级信息\n * @param classInfo 班级信息\n * @param values 更新的值\n * @returns Promise<boolean>\n */ const updateClassInfo = async (classInfo, values)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClassSimple({\n            id: classInfo.id,\n            className: values.className\n        });\n        if (response.data.code === 200) {\n            notification.success(\"班级信息更新成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"更新班级信息失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"更新班级信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"更新班级信息失败\");\n        return false;\n    }\n};\n/**\n * 删除班级\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const deleteClass = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n        if (response.data.code === 200) {\n            notification.success(\"班级删除成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"删除班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"删除班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"删除班级失败\");\n        return false;\n    }\n};\n/**\n * 转移班级\n * @param classId 班级ID\n * @param newTeacherId 新教师ID\n * @param transferType 转移类型\n * @returns Promise<boolean>\n */ const transferClass = async (classId, newTeacherId, transferType)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.transferClass(classId, newTeacherId, transferType);\n        if (response.data.code === 200) {\n            notification.success(\"班级转移成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"转移班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"转移班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转移班级失败\");\n        return false;\n    }\n};\n/**\n * 生成班级邀请码\n * @param classId 班级ID\n * @returns Promise<string | null>\n */ const generateClassInviteCode = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.generateInviteCode(classId);\n        if (response.data.code === 200) {\n            const inviteCode = response.data.data.inviteCode;\n            notification.success(\"邀请码生成成功\");\n            return inviteCode;\n        } else {\n            notification.error(response.data.message || \"生成邀请码失败\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"生成邀请码失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"生成邀请码失败\");\n        return null;\n    }\n};\n/**\n * 搜索教师\n * @param phone 教师手机号\n * @returns Promise<any | null>\n */ const searchTeacherByPhone = async (phone)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.searchTeacherByPhone(phone);\n        if (response.data.code === 200) {\n            return response.data.data;\n        } else {\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n            notification.error(response.data.message || \"未找到该教师\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"搜索教师失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        return null;\n    }\n};\n/**\n * 导出学生信息\n * @param students 学生列表\n * @param className 班级名称\n */ const exportStudentsData = (students, className)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        if (students.length === 0) {\n            notification.warning(\"没有学生数据可以导出\");\n            return;\n        }\n        // 准备导出数据\n        const exportData = students.map((student, index)=>{\n            var _student_currentTemplate;\n            return {\n                序号: index + 1,\n                学号: student.studentNumber || \"\",\n                姓名: student.nickName || \"\",\n                总积分: student.totalPoints || 0,\n                可用积分: student.availablePoints || 0,\n                当前模板: ((_student_currentTemplate = student.currentTemplate) === null || _student_currentTemplate === void 0 ? void 0 : _student_currentTemplate.templateName) || \"无\"\n            };\n        });\n        // 转换为CSV格式\n        const headers = Object.keys(exportData[0]);\n        const csvContent = [\n            headers.join(\",\"),\n            ...exportData.map((row)=>headers.map((header)=>'\"'.concat(row[header], '\"')).join(\",\"))\n        ].join(\"\\n\");\n        // 创建下载链接\n        const blob = new Blob([\n            \"\\uFEFF\" + csvContent\n        ], {\n            type: \"text/csv;charset=utf-8;\"\n        });\n        const link = document.createElement(\"a\");\n        const url = URL.createObjectURL(blob);\n        link.setAttribute(\"href\", url);\n        link.setAttribute(\"download\", \"\".concat(className, \"_学生信息_\").concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        link.style.visibility = \"hidden\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        notification.success(\"学生信息导出成功\");\n    } catch (error) {\n        console.error(\"导出学生信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(\"导出学生信息失败\");\n    }\n};\n/**\n * 使用API导出学生信息\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const exportStudentsViaAPI = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const hideLoading = notification.loading(\"正在导出学生信息...\");\n        const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_2__.studentApi.exportStudents(classId);\n        if (hideLoading) {\n            hideLoading.close();\n        }\n        if (response.data.code === 200) {\n            notification.success(\"导出学生成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"导出学生失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"导出学生失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"导出学生失败，请稍后重试\");\n        return false;\n    }\n};\n/**\n * 验证班级名称\n * @param className 班级名称\n * @returns { isValid: boolean; message?: string }\n */ const validateClassName = (className)=>{\n    if (!className || className.trim().length === 0) {\n        return {\n            isValid: false,\n            message: \"班级名称不能为空\"\n        };\n    }\n    if (className.length > 50) {\n        return {\n            isValid: false,\n            message: \"班级名称不能超过50个字符\"\n        };\n    }\n    // 检查特殊字符\n    const invalidChars = /[<>:\"/\\\\|?*]/;\n    if (invalidChars.test(className)) {\n        return {\n            isValid: false,\n            message: '班级名称不能包含特殊字符 < > : \" / \\\\ | ? *'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * 编辑班级信息（增强版）\n * @param classId 班级ID\n * @param values 班级信息\n * @returns Promise<{ success: boolean; data?: any }>\n */ const editClassInfo = async (classId, values)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        console.log(\"开始编辑班级:\", {\n            classId: classId,\n            values: values\n        });\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            className: values.className,\n            grade: values.grade || \"\"\n        });\n        console.log(\"编辑班级API响应:\", response);\n        if (response.data.code === 200) {\n            console.log(\"编辑班级成功\");\n            notification.success(\"编辑班级成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            console.error(\"编辑班级失败 - API返回错误:\", {\n                code: response.data.code,\n                message: response.data.message,\n                data: response.data\n            });\n            notification.error(response.data.message || \"编辑班级失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"编辑班级失败 - 请求异常:\", {\n            error: error,\n            message: error.message,\n            response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n            status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n        });\n        notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 移出协助教师\n * @param classId 班级ID\n * @returns Promise<{ success: boolean; data?: any }>\n */ const removeAssistantTeacher = async (classId)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            assistantTeacherId: 0\n        });\n        if (response.data.code === 200) {\n            notification.success(\"移出协助教师成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            notification.error(response.data.message || \"移出协助教师失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"移出协助教师失败:\", error);\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 删除班级（增强版，包含学生检查）\n * @param classId 班级ID\n * @param students 学生列表\n * @param onSuccess 成功回调\n * @returns Promise<boolean>\n */ const deleteClassWithCheck = async (classId, students, onSuccess)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    // 先检查是否有学生\n    if (students.length > 0) {\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning({\n            title: \"无法删除班级\",\n            content: \"该班级还有 \".concat(students.length, \" 名学生，请先移出所有学生后再删除班级。\"),\n            okText: \"确定\"\n        });\n        return false;\n    }\n    return new Promise((resolve)=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].confirm({\n            title: \"确认删除班级\",\n            content: \"删除后无法恢复，确定要删除这个班级吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        resolve(true);\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                        resolve(false);\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                    resolve(false);\n                }\n            },\n            onCancel: ()=>{\n                resolve(false);\n            }\n        });\n    });\n};\n// 学校数据缓存\nlet schoolsCache = null;\nconst SCHOOL_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\n/**\n * 获取用户关联的学校列表（支持缓存）\n * @param useCache 是否使用缓存，默认true\n * @returns Promise<School[]>\n */ const fetchUserSchools = async function() {\n    let useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    try {\n        // 检查缓存\n        if (useCache && schoolsCache && Date.now() - schoolsCache.timestamp < SCHOOL_CACHE_DURATION) {\n            console.log(\"使用学校列表缓存\");\n            return schoolsCache.data;\n        }\n        console.log(\"=== 开始获取用户学校列表 ===\");\n        // 从localStorage获取用户信息\n        const userInfo = localStorage.getItem(\"user\");\n        if (!userInfo) {\n            console.error(\"未找到用户信息，请检查登录状态\");\n            throw new Error(\"用户未登录\");\n        }\n        const user = JSON.parse(userInfo);\n        console.log(\"解析的用户信息:\", user);\n        // 根据项目中其他组件的使用方式，尝试多种可能的用户ID字段名\n        const userId = user.id || user.userId || user.teacherId;\n        if (!userId) {\n            console.error(\"用户信息中未找到ID字段:\", user);\n            throw new Error(\"用户ID不存在\");\n        }\n        console.log(\"使用的用户ID:\", userId);\n        // 使用项目中已有的API函数\n        const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_3__.schoolApi.getUserSchools();\n        console.log(\"获取学校数据:\", response);\n        if (response.data.code === 200 && response.data.data) {\n            // 确保数据是数组\n            const schoolsData = Array.isArray(response.data.data) ? response.data.data : [];\n            console.log(\"成功获取学校列表:\", schoolsData);\n            // 更新缓存\n            schoolsCache = {\n                data: schoolsData,\n                timestamp: Date.now()\n            };\n            return schoolsData;\n        } else {\n            console.error(\"API返回错误:\", response.data);\n            throw new Error(\"获取学校列表失败: \".concat(response.data.msg || \"未知错误\"));\n        }\n    } catch (error) {\n        console.error(\"获取学校列表失败:\", error);\n        throw error;\n    }\n};\n/**\n * 清除学校列表缓存\n */ const clearSchoolsCache = ()=>{\n    schoolsCache = null;\n    console.log(\"已清除学校列表缓存\");\n};\n/**\n * 获取学校缓存信息\n * @returns 缓存信息或null\n */ const getSchoolsCacheInfo = ()=>{\n    if (!schoolsCache) return null;\n    const isExpired = Date.now() - schoolsCache.timestamp >= SCHOOL_CACHE_DURATION;\n    return {\n        dataCount: schoolsCache.data.length,\n        timestamp: schoolsCache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : SCHOOL_CACHE_DURATION - (Date.now() - schoolsCache.timestamp)\n    };\n};\n/**\n * 预加载学校数据\n * @param delay 延迟时间（毫秒），默认1000ms\n */ const preloadSchoolsData = function() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1000;\n    setTimeout(async ()=>{\n        try {\n            await fetchUserSchools();\n            console.log(\"学校数据预加载完成\");\n        } catch (error) {\n            console.error(\"学校数据预加载失败:\", error);\n        }\n    }, delay);\n};\n// 缓存持续时间：5分钟\nconst TEACHER_CACHE_DURATION = 5 * 60 * 1000;\n// 全局缓存对象\nlet teacherClassesCache = {};\n/**\n * 获取教师管理的学校列表\n * @returns Promise<School[]>\n */ const fetchTeacherSchools = async ()=>{\n    try {\n        var _response_data, _response_data1, _response_data2, _response_data3;\n        const { getTeacherSchools } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\"));\n        console.log(\"=== 开始获取教师学校列表 ===\");\n        const response = await getTeacherSchools();\n        console.log(\"获取教师学校列表API响应:\", response);\n        // 检查多种可能的响应格式\n        let schoolList = [];\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n            schoolList = response.data.data;\n        } else if (((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.status) === 200 && ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.data)) {\n            schoolList = response.data.data;\n        } else if (Array.isArray(response.data)) {\n            schoolList = response.data;\n        }\n        if (schoolList.length > 0) {\n            console.log(\"成功获取教师学校列表，数量:\", schoolList.length);\n            return schoolList;\n        } else {\n            console.warn(\"教师学校列表为空\");\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取教师学校列表失败:\", error);\n        throw new Error(\"获取学校列表失败，请检查网络连接\");\n    }\n};\n/**\n * 获取教师在指定学校的班级列表（支持缓存）\n * @param schoolId 学校ID\n * @param teacherId 教师ID\n * @param useCache 是否使用缓存，默认true\n * @returns Promise<any[]>\n */ const fetchTeacherClasses = async function(schoolId, teacherId) {\n    let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        var _response_data;\n        // 验证参数\n        if (!schoolId || !teacherId) {\n            console.error(\"获取班级列表参数无效:\", {\n                schoolId,\n                teacherId\n            });\n            return [];\n        }\n        // 检查缓存\n        if (useCache && teacherClassesCache[schoolId] && Date.now() - teacherClassesCache[schoolId].timestamp < TEACHER_CACHE_DURATION) {\n            console.log(\"使用教师班级列表缓存:\", schoolId, \"数量:\", teacherClassesCache[schoolId].data.length);\n            return teacherClassesCache[schoolId].data;\n        }\n        const { classApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\"));\n        console.log(\"从服务器获取教师班级列表:\", {\n            schoolId,\n            teacherId,\n            useCache\n        });\n        const response = await classApi.getTeacherClasses(schoolId, teacherId);\n        console.log(\"教师班级列表API响应:\", response);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            const classList = response.data.data || [];\n            // 更新缓存\n            updateTeacherClassCache(schoolId, classList);\n            console.log(\"成功获取教师班级列表，数量:\", classList.length, \"已缓存\");\n            return classList;\n        } else {\n            var _response_data1;\n            console.error(\"API返回错误:\", response.data);\n            // 如果API返回错误但有缓存数据，返回缓存数据\n            if (teacherClassesCache[schoolId]) {\n                console.log(\"API失败，使用缓存数据\");\n                return teacherClassesCache[schoolId].data;\n            }\n            throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"获取班级列表失败\");\n        }\n    } catch (error) {\n        console.error(\"获取教师班级列表失败:\", error);\n        // 如果网络错误但有缓存数据，返回缓存数据\n        if (teacherClassesCache[schoolId]) {\n            console.log(\"网络错误，使用缓存数据\");\n            return teacherClassesCache[schoolId].data;\n        }\n        throw new Error(\"获取班级列表失败，请检查网络连接\");\n    }\n};\n/**\n * 更新教师班级缓存\n * @param schoolId 学校ID\n * @param newClasses 新的班级列表\n */ const updateTeacherClassCache = (schoolId, newClasses)=>{\n    teacherClassesCache[schoolId] = {\n        data: newClasses,\n        timestamp: Date.now(),\n        schoolId\n    };\n    console.log(\"已更新教师班级缓存:\", schoolId, \"班级数量:\", newClasses.length);\n};\n/**\n * 清除教师班级缓存\n * @param schoolId 可选，指定学校ID。不传则清除所有缓存\n */ const clearTeacherClassCache = (schoolId)=>{\n    if (schoolId) {\n        delete teacherClassesCache[schoolId];\n        console.log(\"已清除指定学校的教师班级缓存:\", schoolId);\n    } else {\n        teacherClassesCache = {};\n        console.log(\"已清除所有教师班级缓存\");\n    }\n};\n/**\n * 获取教师班级缓存状态\n * @param schoolId 学校ID\n * @returns 缓存信息或null\n */ const getTeacherClassCacheInfo = (schoolId)=>{\n    const cache = teacherClassesCache[schoolId];\n    if (!cache) return null;\n    const isExpired = Date.now() - cache.timestamp >= TEACHER_CACHE_DURATION;\n    return {\n        schoolId: cache.schoolId,\n        dataCount: cache.data.length,\n        timestamp: cache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)\n    };\n};\n/**\n * 处理学校选择的业务逻辑\n * @param school 选中的学校\n * @param teacherId 教师ID\n * @param callbacks 回调函数集合\n */ const handleSchoolSelection = async (school, teacherId, callbacks)=>{\n    try {\n        var // 通知学校选择变化\n        _callbacks_onSchoolSelect, // 切换到班级管理页面\n        _callbacks_onMenuItemClick, // 通知开始加载班级数据\n        _callbacks_onClassesUpdate, // 通知班级数据更新完成\n        _callbacks_onClassesUpdate1;\n        console.log(\"处理学校选择:\", school.schoolName);\n        (_callbacks_onSchoolSelect = callbacks.onSchoolSelect) === null || _callbacks_onSchoolSelect === void 0 ? void 0 : _callbacks_onSchoolSelect.call(callbacks, school);\n        (_callbacks_onMenuItemClick = callbacks.onMenuItemClick) === null || _callbacks_onMenuItemClick === void 0 ? void 0 : _callbacks_onMenuItemClick.call(callbacks, \"班级管理\");\n        (_callbacks_onClassesUpdate = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate === void 0 ? void 0 : _callbacks_onClassesUpdate.call(callbacks, [], true, null);\n        // 获取班级列表\n        const classList = await fetchTeacherClasses(school.id, teacherId);\n        (_callbacks_onClassesUpdate1 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate1 === void 0 ? void 0 : _callbacks_onClassesUpdate1.call(callbacks, classList, false, null);\n        console.log(\"学校选择处理完成，班级数量:\", classList.length);\n    } catch (error) {\n        var _callbacks_onClassesUpdate2;\n        console.error(\"处理学校选择失败:\", error);\n        (_callbacks_onClassesUpdate2 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate2 === void 0 ? void 0 : _callbacks_onClassesUpdate2.call(callbacks, [], false, error.message || \"获取班级列表失败\");\n    }\n};\n/**\n * 初始化学校选择模态框状态\n * @returns SchoolModalState\n */ const initSchoolModalState = ()=>({\n        schools: [],\n        loading: false,\n        error: null,\n        mounted: false\n    });\n/**\n * 处理学校选择模态框的数据加载\n * @param userId 用户ID\n * @param setState 状态更新函数\n * @param useCache 是否使用缓存\n */ const handleSchoolModalDataLoad = async function(userId, setState) {\n    let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    if (!userId) {\n        setState({\n            error: \"用户未登录\"\n        });\n        return;\n    }\n    try {\n        setState({\n            loading: true,\n            error: null\n        });\n        const schoolsData = await fetchUserSchools(useCache);\n        setState({\n            schools: schoolsData,\n            loading: false\n        });\n    } catch (err) {\n        console.error(\"获取学校列表失败:\", err);\n        setState({\n            error: \"获取学校列表失败，请稍后重试\",\n            loading: false\n        });\n    }\n};\n/**\n * 处理学校选择模态框的重试逻辑\n * @param setState 状态更新函数\n * @param userId 用户ID\n */ const handleSchoolModalRetry = async (setState, userId)=>{\n    await handleSchoolModalDataLoad(userId, setState, false);\n};\n/**\n * 获取班级学生列表（带通知）\n * @param classId 班级ID\n * @param notification 通知组件\n * @returns Promise<Student[]>\n */ const fetchClassStudentsWithNotification = async (classId, notification)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getClassStudents(classId);\n        if (response.data.code === 200) {\n            return response.data.data || [];\n        } else {\n            console.error(\"获取学生列表失败:\", response.data.message);\n            notification.error(\"获取学生列表失败\");\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取学生列表失败:\", error);\n        notification.error(\"获取学生列表失败\");\n        return [];\n    }\n};\n/**\n * 获取学生能量信息（优化版本）\n * @param userIds 用户ID数组\n * @param notification 通知组件\n * @returns Promise<Map<number, number>>\n */ const fetchStudentPoints = async (userIds, notification)=>{\n    if (userIds.length === 0) {\n        return new Map();\n    }\n    try {\n        const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n        console.log(\"开始获取学生能量信息，userIds:\", userIds);\n        console.log(\"使用API端点:\", \"\".concat(pointsApi.basePointUrl, \"/batch-total\"));\n        // 设置3秒超时，避免长时间等待影响用户体验\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"请求超时\")), 3000);\n        });\n        const apiPromise = pointsApi.getBatchStudentPoints(userIds);\n        const response = await Promise.race([\n            apiPromise,\n            timeoutPromise\n        ]);\n        const pointsMap = new Map();\n        console.log(\"批量积分响应\", response);\n        if (response.data.code === 200) {\n            const data = response.data.data;\n            // 遍历返回的学生积分数据\n            for(const userId in data){\n                const studentData = data[userId];\n                if (studentData) {\n                    const totalPoints = studentData.totalPoints || 0;\n                    const availablePoints = studentData.availablePoints || 0;\n                    // 修正计算逻辑：与 assign-points-modal.tsx 保持一致\n                    // totalPoints: 总积分, availablePoints: 已使用积分\n                    // 可分配积分 = 总积分 - 已使用积分\n                    const remainingPoints = totalPoints - availablePoints;\n                    pointsMap.set(Number(userId), Math.max(0, remainingPoints));\n                } else {\n                    pointsMap.set(Number(userId), 0);\n                }\n            }\n            // 确保所有请求的用户都有数据\n            userIds.forEach((uid)=>{\n                if (!pointsMap.has(uid)) {\n                    pointsMap.set(uid, 0);\n                }\n            });\n            return pointsMap;\n        } else {\n            // API返回错误，设置默认值但不显示错误提示（避免干扰用户体验）\n            userIds.forEach((uid)=>pointsMap.set(uid, 0));\n            console.warn(\"获取学生能量信息失败，使用默认值:\", response.data.message);\n            return pointsMap;\n        }\n    } catch (error) {\n        var _error_response, _error_response1;\n        console.error(\"批量获取学生能量失败:\", error);\n        // 设置默认值，让用户可以继续操作\n        const pointsMap = new Map();\n        userIds.forEach((uid)=>pointsMap.set(uid, 0));\n        // 根据错误类型决定是否显示提示\n        if (error.message === \"请求超时\") {\n            console.warn(\"获取能量信息超时，使用默认值，用户可继续操作\");\n        // 不显示错误提示，避免干扰用户体验\n        } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n            console.warn(\"服务器错误，使用默认能量值\");\n        // 服务器错误时也不显示提示，让用户可以继续操作\n        } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n            // 权限问题可能需要用户知道\n            notification.warning(\"暂无法获取能量信息，已设置默认值\");\n        }\n        // 其他错误也不显示提示，保持良好的用户体验\n        return pointsMap;\n    }\n};\n/**\n * 计算所有学生的最低可分配能量\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 最低可分配能量\n */ const getMinAvailablePoints = (selectedStudents, studentPointsMap)=>{\n    if (selectedStudents.length === 0) return 0;\n    const selectedStudentPoints = selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n    return Math.min(...selectedStudentPoints);\n};\n/**\n * 获取当前分配方式的提示信息\n * @param selectedDistribution 选中的分配方式\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 提示信息对象\n */ const getEnergyDisplayInfo = (selectedDistribution, selectedStudents, studentPointsMap)=>{\n    if (selectedDistribution === \"assign\" || selectedDistribution === \"distribute\") {\n        return {\n            label: \"最低可分配能量\",\n            value: getMinAvailablePoints(selectedStudents, studentPointsMap)\n        };\n    }\n    return {\n        label: \"\",\n        value: 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classUtils.ts\n"));

/***/ })

});