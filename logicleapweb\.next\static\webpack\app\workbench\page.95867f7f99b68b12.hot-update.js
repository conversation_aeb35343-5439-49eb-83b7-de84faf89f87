"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classUtils.ts":
/*!*******************************************!*\
  !*** ./app/workbench/utils/classUtils.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSchoolsCache: function() { return /* binding */ clearSchoolsCache; },\n/* harmony export */   deleteClass: function() { return /* binding */ deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* binding */ deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* binding */ editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* binding */ exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* binding */ exportStudentsViaAPI; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* binding */ fetchClassStudentsWithNotification; },\n/* harmony export */   fetchStudentPoints: function() { return /* binding */ fetchStudentPoints; },\n/* harmony export */   fetchUserSchools: function() { return /* binding */ fetchUserSchools; },\n/* harmony export */   generateClassInviteCode: function() { return /* binding */ generateClassInviteCode; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* binding */ getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* binding */ getMinAvailablePoints; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* binding */ getSchoolsCacheInfo; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* binding */ getTeacherClassCacheInfo; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* binding */ handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* binding */ handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* binding */ handleSchoolSelection; },\n/* harmony export */   initSchoolModalState: function() { return /* binding */ initSchoolModalState; },\n/* harmony export */   preloadSchoolsData: function() { return /* binding */ preloadSchoolsData; },\n/* harmony export */   removeAssistantTeacher: function() { return /* binding */ removeAssistantTeacher; },\n/* harmony export */   searchTeacherByPhone: function() { return /* binding */ searchTeacherByPhone; },\n/* harmony export */   transferClass: function() { return /* binding */ transferClass; },\n/* harmony export */   updateClassInfo: function() { return /* binding */ updateClassInfo; },\n/* harmony export */   validateClassName: function() { return /* binding */ validateClassName; }\n/* harmony export */ });\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n\n\n\n\n\n/**\n * 班级管理相关工具函数\n */ /**\n * 编辑班级信息\n * @param classInfo 班级信息\n * @param values 更新的值\n * @returns Promise<boolean>\n */ const updateClassInfo = async (classInfo, values)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClassSimple({\n            id: classInfo.id,\n            className: values.className\n        });\n        if (response.data.code === 200) {\n            notification.success(\"班级信息更新成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"更新班级信息失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"更新班级信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"更新班级信息失败\");\n        return false;\n    }\n};\n/**\n * 删除班级\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const deleteClass = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n        if (response.data.code === 200) {\n            notification.success(\"班级删除成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"删除班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"删除班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"删除班级失败\");\n        return false;\n    }\n};\n/**\n * 转移班级\n * @param classId 班级ID\n * @param newTeacherId 新教师ID\n * @param transferType 转移类型\n * @returns Promise<boolean>\n */ const transferClass = async (classId, newTeacherId, transferType)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.transferClass(classId, newTeacherId, transferType);\n        if (response.data.code === 200) {\n            notification.success(\"班级转移成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"转移班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"转移班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转移班级失败\");\n        return false;\n    }\n};\n/**\n * 生成班级邀请码\n * @param classId 班级ID\n * @returns Promise<string | null>\n */ const generateClassInviteCode = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.generateInviteCode(classId);\n        if (response.data.code === 200) {\n            const inviteCode = response.data.data.inviteCode;\n            notification.success(\"邀请码生成成功\");\n            return inviteCode;\n        } else {\n            notification.error(response.data.message || \"生成邀请码失败\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"生成邀请码失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"生成邀请码失败\");\n        return null;\n    }\n};\n/**\n * 搜索教师\n * @param phone 教师手机号\n * @returns Promise<any | null>\n */ const searchTeacherByPhone = async (phone)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.searchTeacherByPhone(phone);\n        if (response.data.code === 200) {\n            return response.data.data;\n        } else {\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n            notification.error(response.data.message || \"未找到该教师\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"搜索教师失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        return null;\n    }\n};\n/**\n * 导出学生信息\n * @param students 学生列表\n * @param className 班级名称\n */ const exportStudentsData = (students, className)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        if (students.length === 0) {\n            notification.warning(\"没有学生数据可以导出\");\n            return;\n        }\n        // 准备导出数据\n        const exportData = students.map((student, index)=>{\n            var _student_currentTemplate;\n            return {\n                序号: index + 1,\n                学号: student.studentNumber || \"\",\n                姓名: student.nickName || \"\",\n                总积分: student.totalPoints || 0,\n                可用积分: student.availablePoints || 0,\n                当前模板: ((_student_currentTemplate = student.currentTemplate) === null || _student_currentTemplate === void 0 ? void 0 : _student_currentTemplate.templateName) || \"无\"\n            };\n        });\n        // 转换为CSV格式\n        const headers = Object.keys(exportData[0]);\n        const csvContent = [\n            headers.join(\",\"),\n            ...exportData.map((row)=>headers.map((header)=>'\"'.concat(row[header], '\"')).join(\",\"))\n        ].join(\"\\n\");\n        // 创建下载链接\n        const blob = new Blob([\n            \"\\uFEFF\" + csvContent\n        ], {\n            type: \"text/csv;charset=utf-8;\"\n        });\n        const link = document.createElement(\"a\");\n        const url = URL.createObjectURL(blob);\n        link.setAttribute(\"href\", url);\n        link.setAttribute(\"download\", \"\".concat(className, \"_学生信息_\").concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        link.style.visibility = \"hidden\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        notification.success(\"学生信息导出成功\");\n    } catch (error) {\n        console.error(\"导出学生信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(\"导出学生信息失败\");\n    }\n};\n/**\n * 使用API导出学生信息\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const exportStudentsViaAPI = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const hideLoading = notification.loading(\"正在导出学生信息...\");\n        const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_2__.studentApi.exportStudents(classId);\n        if (hideLoading) {\n            hideLoading.close();\n        }\n        if (response.data.code === 200) {\n            notification.success(\"导出学生成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"导出学生失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"导出学生失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"导出学生失败，请稍后重试\");\n        return false;\n    }\n};\n/**\n * 验证班级名称\n * @param className 班级名称\n * @returns { isValid: boolean; message?: string }\n */ const validateClassName = (className)=>{\n    if (!className || className.trim().length === 0) {\n        return {\n            isValid: false,\n            message: \"班级名称不能为空\"\n        };\n    }\n    if (className.length > 50) {\n        return {\n            isValid: false,\n            message: \"班级名称不能超过50个字符\"\n        };\n    }\n    // 检查特殊字符\n    const invalidChars = /[<>:\"/\\\\|?*]/;\n    if (invalidChars.test(className)) {\n        return {\n            isValid: false,\n            message: '班级名称不能包含特殊字符 < > : \" / \\\\ | ? *'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * 编辑班级信息（增强版）\n * @param classId 班级ID\n * @param values 班级信息\n * @returns Promise<{ success: boolean; data?: any }>\n */ const editClassInfo = async (classId, values)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        console.log(\"开始编辑班级:\", {\n            classId: classId,\n            values: values\n        });\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            className: values.className,\n            grade: values.grade || \"\"\n        });\n        console.log(\"编辑班级API响应:\", response);\n        if (response.data.code === 200) {\n            console.log(\"编辑班级成功\");\n            notification.success(\"编辑班级成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            console.error(\"编辑班级失败 - API返回错误:\", {\n                code: response.data.code,\n                message: response.data.message,\n                data: response.data\n            });\n            notification.error(response.data.message || \"编辑班级失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"编辑班级失败 - 请求异常:\", {\n            error: error,\n            message: error.message,\n            response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n            status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n        });\n        notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 移出协助教师\n * @param classId 班级ID\n * @returns Promise<{ success: boolean; data?: any }>\n */ const removeAssistantTeacher = async (classId)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            assistantTeacherId: 0\n        });\n        if (response.data.code === 200) {\n            notification.success(\"移出协助教师成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            notification.error(response.data.message || \"移出协助教师失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"移出协助教师失败:\", error);\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 删除班级（增强版，包含学生检查）\n * @param classId 班级ID\n * @param students 学生列表\n * @param onSuccess 成功回调\n * @returns Promise<boolean>\n */ const deleteClassWithCheck = async (classId, students, onSuccess)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    // 先检查是否有学生\n    if (students.length > 0) {\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning({\n            title: \"无法删除班级\",\n            content: \"该班级还有 \".concat(students.length, \" 名学生，请先移出所有学生后再删除班级。\"),\n            okText: \"确定\"\n        });\n        return false;\n    }\n    return new Promise((resolve)=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].confirm({\n            title: \"确认删除班级\",\n            content: \"删除后无法恢复，确定要删除这个班级吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        resolve(true);\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                        resolve(false);\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                    resolve(false);\n                }\n            },\n            onCancel: ()=>{\n                resolve(false);\n            }\n        });\n    });\n};\n// 学校数据缓存\nlet schoolsCache = null;\nconst SCHOOL_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\n/**\n * 获取用户关联的学校列表（支持缓存）\n * @param useCache 是否使用缓存，默认true\n * @returns Promise<School[]>\n */ const fetchUserSchools = async function() {\n    let useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    try {\n        // 检查缓存\n        if (useCache && schoolsCache && Date.now() - schoolsCache.timestamp < SCHOOL_CACHE_DURATION) {\n            console.log(\"使用学校列表缓存\");\n            return schoolsCache.data;\n        }\n        console.log(\"=== 开始获取用户学校列表 ===\");\n        // 从localStorage获取用户信息\n        const userInfo = localStorage.getItem(\"user\");\n        if (!userInfo) {\n            console.error(\"未找到用户信息，请检查登录状态\");\n            throw new Error(\"用户未登录\");\n        }\n        const user = JSON.parse(userInfo);\n        console.log(\"解析的用户信息:\", user);\n        // 根据项目中其他组件的使用方式，尝试多种可能的用户ID字段名\n        const userId = user.id || user.userId || user.teacherId;\n        if (!userId) {\n            console.error(\"用户信息中未找到ID字段:\", user);\n            throw new Error(\"用户ID不存在\");\n        }\n        console.log(\"使用的用户ID:\", userId);\n        // 使用项目中已有的API函数\n        const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_3__.schoolApi.getUserSchools();\n        console.log(\"获取学校数据:\", response);\n        if (response.data.code === 200 && response.data.data) {\n            // 确保数据是数组\n            const schoolsData = Array.isArray(response.data.data) ? response.data.data : [];\n            console.log(\"成功获取学校列表:\", schoolsData);\n            // 更新缓存\n            schoolsCache = {\n                data: schoolsData,\n                timestamp: Date.now()\n            };\n            return schoolsData;\n        } else {\n            console.error(\"API返回错误:\", response.data);\n            throw new Error(\"获取学校列表失败: \".concat(response.data.msg || \"未知错误\"));\n        }\n    } catch (error) {\n        console.error(\"获取学校列表失败:\", error);\n        throw error;\n    }\n};\n/**\n * 清除学校列表缓存\n */ const clearSchoolsCache = ()=>{\n    schoolsCache = null;\n    console.log(\"已清除学校列表缓存\");\n};\n/**\n * 获取学校缓存信息\n * @returns 缓存信息或null\n */ const getSchoolsCacheInfo = ()=>{\n    if (!schoolsCache) return null;\n    const isExpired = Date.now() - schoolsCache.timestamp >= SCHOOL_CACHE_DURATION;\n    return {\n        dataCount: schoolsCache.data.length,\n        timestamp: schoolsCache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : SCHOOL_CACHE_DURATION - (Date.now() - schoolsCache.timestamp)\n    };\n};\n/**\n * 预加载学校数据\n * @param delay 延迟时间（毫秒），默认1000ms\n */ const preloadSchoolsData = function() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1000;\n    setTimeout(async ()=>{\n        try {\n            await fetchUserSchools();\n            console.log(\"学校数据预加载完成\");\n        } catch (error) {\n            console.error(\"学校数据预加载失败:\", error);\n        }\n    }, delay);\n};\n// 缓存持续时间：5分钟\nconst TEACHER_CACHE_DURATION = 5 * 60 * 1000;\n// 全局缓存对象\nlet teacherClassesCache = {};\n// 已移除：fetchTeacherSchools - 请使用 classService.getTeacherSchools()\n// 已移除：fetchTeacherClasses - 请使用 classService.getSchoolClasses()\n// 已移除：updateTeacherClassCache - 请使用 classService 的缓存系统\n// 已移除：clearTeacherClassCache - 请使用 classService 的缓存系统\n// 已移除：debugClassCache - 请使用 classService.debugCache()\n/**\n * 获取教师班级缓存状态\n * @param schoolId 学校ID\n * @returns 缓存信息或null\n */ const getTeacherClassCacheInfo = (schoolId)=>{\n    const cache = teacherClassesCache[schoolId];\n    if (!cache) return null;\n    const isExpired = Date.now() - cache.timestamp >= TEACHER_CACHE_DURATION;\n    return {\n        schoolId: cache.schoolId,\n        dataCount: cache.data.length,\n        timestamp: cache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)\n    };\n};\n/**\n * 处理学校选择的业务逻辑\n * @param school 选中的学校\n * @param teacherId 教师ID\n * @param callbacks 回调函数集合\n */ const handleSchoolSelection = async (school, teacherId, callbacks)=>{\n    try {\n        var // 通知学校选择变化\n        _callbacks_onSchoolSelect, // 切换到班级管理页面\n        _callbacks_onMenuItemClick, // 通知开始加载班级数据\n        _callbacks_onClassesUpdate, // 通知班级数据更新完成\n        _callbacks_onClassesUpdate1;\n        console.log(\"处理学校选择:\", school.schoolName);\n        (_callbacks_onSchoolSelect = callbacks.onSchoolSelect) === null || _callbacks_onSchoolSelect === void 0 ? void 0 : _callbacks_onSchoolSelect.call(callbacks, school);\n        (_callbacks_onMenuItemClick = callbacks.onMenuItemClick) === null || _callbacks_onMenuItemClick === void 0 ? void 0 : _callbacks_onMenuItemClick.call(callbacks, \"班级管理\");\n        (_callbacks_onClassesUpdate = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate === void 0 ? void 0 : _callbacks_onClassesUpdate.call(callbacks, [], true, null);\n        // 获取班级列表\n        const classList = await fetchTeacherClasses(school.id, teacherId);\n        (_callbacks_onClassesUpdate1 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate1 === void 0 ? void 0 : _callbacks_onClassesUpdate1.call(callbacks, classList, false, null);\n        console.log(\"学校选择处理完成，班级数量:\", classList.length);\n    } catch (error) {\n        var _callbacks_onClassesUpdate2;\n        console.error(\"处理学校选择失败:\", error);\n        (_callbacks_onClassesUpdate2 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate2 === void 0 ? void 0 : _callbacks_onClassesUpdate2.call(callbacks, [], false, error.message || \"获取班级列表失败\");\n    }\n};\n/**\n * 初始化学校选择模态框状态\n * @returns SchoolModalState\n */ const initSchoolModalState = ()=>({\n        schools: [],\n        loading: false,\n        error: null,\n        mounted: false\n    });\n/**\n * 处理学校选择模态框的数据加载\n * @param userId 用户ID\n * @param setState 状态更新函数\n * @param useCache 是否使用缓存\n */ const handleSchoolModalDataLoad = async function(userId, setState) {\n    let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    if (!userId) {\n        setState({\n            error: \"用户未登录\"\n        });\n        return;\n    }\n    try {\n        setState({\n            loading: true,\n            error: null\n        });\n        const schoolsData = await fetchUserSchools(useCache);\n        setState({\n            schools: schoolsData,\n            loading: false\n        });\n    } catch (err) {\n        console.error(\"获取学校列表失败:\", err);\n        setState({\n            error: \"获取学校列表失败，请稍后重试\",\n            loading: false\n        });\n    }\n};\n/**\n * 处理学校选择模态框的重试逻辑\n * @param setState 状态更新函数\n * @param userId 用户ID\n */ const handleSchoolModalRetry = async (setState, userId)=>{\n    await handleSchoolModalDataLoad(userId, setState, false);\n};\n/**\n * 获取班级学生列表（带通知）\n * @param classId 班级ID\n * @param notification 通知组件\n * @returns Promise<Student[]>\n */ const fetchClassStudentsWithNotification = async (classId, notification)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getClassStudents(classId);\n        if (response.data.code === 200) {\n            return response.data.data || [];\n        } else {\n            console.error(\"获取学生列表失败:\", response.data.message);\n            notification.error(\"获取学生列表失败\");\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取学生列表失败:\", error);\n        notification.error(\"获取学生列表失败\");\n        return [];\n    }\n};\n/**\n * 获取学生能量信息（优化版本）\n * @param userIds 用户ID数组\n * @param notification 通知组件\n * @returns Promise<Map<number, number>>\n */ const fetchStudentPoints = async (userIds, notification)=>{\n    if (userIds.length === 0) {\n        return new Map();\n    }\n    try {\n        const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n        console.log(\"开始获取学生能量信息，userIds:\", userIds);\n        console.log(\"使用API端点:\", \"\".concat(pointsApi.basePointUrl, \"/batch-total\"));\n        // 设置3秒超时，避免长时间等待影响用户体验\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"请求超时\")), 3000);\n        });\n        const apiPromise = pointsApi.getBatchStudentPoints(userIds);\n        const response = await Promise.race([\n            apiPromise,\n            timeoutPromise\n        ]);\n        const pointsMap = new Map();\n        console.log(\"批量积分响应\", response);\n        if (response.data.code === 200) {\n            const data = response.data.data;\n            // 遍历返回的学生积分数据\n            for(const userId in data){\n                const studentData = data[userId];\n                if (studentData) {\n                    const totalPoints = studentData.totalPoints || 0;\n                    const availablePoints = studentData.availablePoints || 0;\n                    // 修正计算逻辑：与 assign-points-modal.tsx 保持一致\n                    // totalPoints: 总积分, availablePoints: 已使用积分\n                    // 可分配积分 = 总积分 - 已使用积分\n                    const remainingPoints = totalPoints - availablePoints;\n                    pointsMap.set(Number(userId), Math.max(0, remainingPoints));\n                } else {\n                    pointsMap.set(Number(userId), 0);\n                }\n            }\n            // 确保所有请求的用户都有数据\n            userIds.forEach((uid)=>{\n                if (!pointsMap.has(uid)) {\n                    pointsMap.set(uid, 0);\n                }\n            });\n            return pointsMap;\n        } else {\n            // API返回错误，设置默认值但不显示错误提示（避免干扰用户体验）\n            userIds.forEach((uid)=>pointsMap.set(uid, 0));\n            console.warn(\"获取学生能量信息失败，使用默认值:\", response.data.message);\n            return pointsMap;\n        }\n    } catch (error) {\n        var _error_response, _error_response1;\n        console.error(\"批量获取学生能量失败:\", error);\n        // 设置默认值，让用户可以继续操作\n        const pointsMap = new Map();\n        userIds.forEach((uid)=>pointsMap.set(uid, 0));\n        // 根据错误类型决定是否显示提示\n        if (error.message === \"请求超时\") {\n            console.warn(\"获取能量信息超时，使用默认值，用户可继续操作\");\n        // 不显示错误提示，避免干扰用户体验\n        } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n            console.warn(\"服务器错误，使用默认能量值\");\n        // 服务器错误时也不显示提示，让用户可以继续操作\n        } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n            // 权限问题可能需要用户知道\n            notification.warning(\"暂无法获取能量信息，已设置默认值\");\n        }\n        // 其他错误也不显示提示，保持良好的用户体验\n        return pointsMap;\n    }\n};\n/**\n * 计算所有学生的最低可分配能量\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 最低可分配能量\n */ const getMinAvailablePoints = (selectedStudents, studentPointsMap)=>{\n    if (selectedStudents.length === 0) return 0;\n    const selectedStudentPoints = selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n    return Math.min(...selectedStudentPoints);\n};\n/**\n * 获取当前分配方式的提示信息\n * @param selectedDistribution 选中的分配方式\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 提示信息对象\n */ const getEnergyDisplayInfo = (selectedDistribution, selectedStudents, studentPointsMap)=>{\n    if (selectedDistribution === \"assign\" || selectedDistribution === \"distribute\") {\n        return {\n            label: \"最低可分配能量\",\n            value: getMinAvailablePoints(selectedStudents, studentPointsMap)\n        };\n    }\n    return {\n        label: \"\",\n        value: 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/utils/index.ts":
/*!**************************************!*\
  !*** ./app/workbench/utils/index.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetNotification: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.GetNotification; },\n/* harmony export */   addStudentToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.addStudentToClass; },\n/* harmony export */   applyTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.applyTemplate; },\n/* harmony export */   assignPointsToStudent: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.assignPointsToStudent; },\n/* harmony export */   assignTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.assignTemplateToStudents; },\n/* harmony export */   batchAssignPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchAssignPoints; },\n/* harmony export */   batchUseKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchUseKeys; },\n/* harmony export */   calculateCompletionRate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateCompletionRate; },\n/* harmony export */   calculateQuickTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateQuickTime; },\n/* harmony export */   calculateTotalAvailablePoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.calculateTotalAvailablePoints; },\n/* harmony export */   clearSchoolsCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.clearSchoolsCache; },\n/* harmony export */   debounce: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.debounce; },\n/* harmony export */   deepClone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.deepClone; },\n/* harmony export */   deleteClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsViaAPI; },\n/* harmony export */   fetchAllClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchAllClassProjects; },\n/* harmony export */   fetchBatchStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchBatchStudentPoints; },\n/* harmony export */   fetchClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchClassProjects; },\n/* harmony export */   fetchClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchClassStudents; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchClassStudentsWithNotification; },\n/* harmony export */   fetchClassTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchClassTasks; },\n/* harmony export */   fetchCompleteClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchCompleteClassStudents; },\n/* harmony export */   fetchCurrentTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchCurrentTemplate; },\n/* harmony export */   fetchFolderTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolderTemplates; },\n/* harmony export */   fetchFolders: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolders; },\n/* harmony export */   fetchKeyRecords: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.fetchKeyRecords; },\n/* harmony export */   fetchMyTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchMyTemplates; },\n/* harmony export */   fetchOfficialTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchOfficialTemplates; },\n/* harmony export */   fetchSingleStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchSingleStudentPoints; },\n/* harmony export */   fetchSpecialTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchSpecialTemplate; },\n/* harmony export */   fetchStudentPoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchStudentPoints; },\n/* harmony export */   fetchStudentsInfo: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchStudentsInfo; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchTeacherClassesForProjects; },\n/* harmony export */   fetchTeacherWorks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchTeacherWorks; },\n/* harmony export */   fetchUserSchools: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchUserSchools; },\n/* harmony export */   fetchWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fetchWorks; },\n/* harmony export */   filterTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.filterTasks; },\n/* harmony export */   filterTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.filterTemplates; },\n/* harmony export */   fixImageUrl: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fixImageUrl; },\n/* harmony export */   fixProjectImageUrl: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fixProjectImageUrl; },\n/* harmony export */   formatAddressDisplay: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatAddressDisplay; },\n/* harmony export */   formatDateTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatDateTime; },\n/* harmony export */   formatFileSize: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatFileSize; },\n/* harmony export */   formatPackageInfo: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.formatPackageInfo; },\n/* harmony export */   formatPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.formatPoints; },\n/* harmony export */   formatPublishTime: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.formatPublishTime; },\n/* harmony export */   formatTaskDate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDate; },\n/* harmony export */   formatTaskDateSimple: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDateSimple; },\n/* harmony export */   formatTimeDisplay: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTimeDisplay; },\n/* harmony export */   generateClassInviteCode: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.generateClassInviteCode; },\n/* harmony export */   generateRandomString: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateRandomString; },\n/* harmony export */   generateStepIndicatorConfig: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateStepIndicatorConfig; },\n/* harmony export */   getAvatarColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarColor; },\n/* harmony export */   getAvatarGradientColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarGradientColor; },\n/* harmony export */   getBatchStudentPackageExpiries: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getBatchStudentPackageExpiries; },\n/* harmony export */   getCurrentDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getCurrentDateTime; },\n/* harmony export */   getCurrentEnergyAmount: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrentEnergyAmount; },\n/* harmony export */   getCurrentUserId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserSchoolId; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getMinAvailablePoints; },\n/* harmony export */   getNextDayDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getNextDayDateTime; },\n/* harmony export */   getRelativeTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.getRelativeTime; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getSchoolsCacheInfo; },\n/* harmony export */   getStudentPackageExpiry: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getStudentPackageExpiry; },\n/* harmony export */   getTaskRemainingTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskRemainingTime; },\n/* harmony export */   getTaskStatus: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatus; },\n/* harmony export */   getTaskStatusColor: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatusColor; },\n/* harmony export */   getTaskTimes: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTaskTimes; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getTeacherClassCacheInfo; },\n/* harmony export */   getTemplateDetails: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTemplateDetails; },\n/* harmony export */   handleDeleteTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleDeleteTask; },\n/* harmony export */   handleEditTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleEditTask; },\n/* harmony export */   handleMouseDown: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseDown; },\n/* harmony export */   handleMouseLeave: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseLeave; },\n/* harmony export */   handleMouseMove: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseMove; },\n/* harmony export */   handleMouseUp: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseUp; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolSelection; },\n/* harmony export */   handleSelectWork: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleSelectWork; },\n/* harmony export */   handleViewTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleViewTask; },\n/* harmony export */   handleViewWork: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.handleViewWork; },\n/* harmony export */   handleWheelScroll: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleWheelScroll; },\n/* harmony export */   importStudentsFromFile: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsFromFile; },\n/* harmony export */   importStudentsToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsToClass; },\n/* harmony export */   initClassProjectsState: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.initClassProjectsState; },\n/* harmony export */   initDragState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initDragState; },\n/* harmony export */   initSchoolModalState: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.initSchoolModalState; },\n/* harmony export */   initTemplateModalData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateModalData; },\n/* harmony export */   initTemplateModalState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplateModalState; },\n/* harmony export */   initTemplatePickerState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplatePickerState; },\n/* harmony export */   initTemplateTaskData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateTaskData; },\n/* harmony export */   initWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initWorksState; },\n/* harmony export */   isValidEmail: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidEmail; },\n/* harmony export */   isValidKeyFormat: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.isValidKeyFormat; },\n/* harmony export */   isValidPhone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidPhone; },\n/* harmony export */   loadMoreWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.loadMoreWorks; },\n/* harmony export */   mergeStudentData: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.mergeStudentData; },\n/* harmony export */   parseExcelKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.parseExcelKeys; },\n/* harmony export */   preloadSchoolsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.preloadSchoolsData; },\n/* harmony export */   prepareBlockAssignment: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.prepareBlockAssignment; },\n/* harmony export */   publishTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.publishTask; },\n/* harmony export */   redeemKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.redeemKey; },\n/* harmony export */   removeAssistantTeacher: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.removeAssistantTeacher; },\n/* harmony export */   removeFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.removeFile; },\n/* harmony export */   removeStudentsFromClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.removeStudentsFromClass; },\n/* harmony export */   removeTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.removeTemplate; },\n/* harmony export */   resetStudentPassword: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.resetStudentPassword; },\n/* harmony export */   resetWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.resetWorksState; },\n/* harmony export */   searchTeacherByPhone: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.searchTeacherByPhone; },\n/* harmony export */   shouldShowEmptyState: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.shouldShowEmptyState; },\n/* harmony export */   showBatchOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showBatchOperationResult; },\n/* harmony export */   showConfirm: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showConfirm; },\n/* harmony export */   showError: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showError; },\n/* harmony export */   showInfo: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showInfo; },\n/* harmony export */   showLoading: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showLoading; },\n/* harmony export */   showOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showOperationResult; },\n/* harmony export */   showRedeemConfirmModal: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.showRedeemConfirmModal; },\n/* harmony export */   showSuccess: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showSuccess; },\n/* harmony export */   showWarning: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showWarning; },\n/* harmony export */   sortProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.sortProjects; },\n/* harmony export */   syncTeacherTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.syncTeacherTemplateToStudents; },\n/* harmony export */   throttle: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.throttle; },\n/* harmony export */   transferClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.transferClass; },\n/* harmony export */   triggerPointsUpdateEvent: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.triggerPointsUpdateEvent; },\n/* harmony export */   uniqueArray: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.uniqueArray; },\n/* harmony export */   updateClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.updateClassInfo; },\n/* harmony export */   updatePersonalTemplateAssignments: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updatePersonalTemplateAssignments; },\n/* harmony export */   updateStudentsTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updateStudentsTemplate; },\n/* harmony export */   uploadFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.uploadFile; },\n/* harmony export */   validateClassName: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.validateClassName; },\n/* harmony export */   validateEnergyDistribution: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateEnergyDistribution; },\n/* harmony export */   validateExcelFile: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validateExcelFile; },\n/* harmony export */   validateKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.validateKey; },\n/* harmony export */   validatePointsAssignment: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignment; },\n/* harmony export */   validatePointsAssignmentModal: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignmentModal; },\n/* harmony export */   validateTaskForm: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskForm; },\n/* harmony export */   validateTaskParams: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskParams; },\n/* harmony export */   validateUploadFiles: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateUploadFiles; }\n/* harmony export */ });\n/* harmony import */ var _baseUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baseUtils */ \"(app-pages-browser)/./app/workbench/utils/baseUtils.ts\");\n/* harmony import */ var _studentUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./studentUtils */ \"(app-pages-browser)/./app/workbench/utils/studentUtils.ts\");\n/* harmony import */ var _templateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templateUtils */ \"(app-pages-browser)/./app/workbench/utils/templateUtils.ts\");\n/* harmony import */ var _pointsUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointsUtils */ \"(app-pages-browser)/./app/workbench/utils/pointsUtils.ts\");\n/* harmony import */ var _classUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./classUtils */ \"(app-pages-browser)/./app/workbench/utils/classUtils.ts\");\n/* harmony import */ var _notificationUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notificationUtils */ \"(app-pages-browser)/./app/workbench/utils/notificationUtils.ts\");\n/* harmony import */ var _taskUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./taskUtils */ \"(app-pages-browser)/./app/workbench/utils/taskUtils.ts\");\n/* harmony import */ var _keyUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./keyUtils */ \"(app-pages-browser)/./app/workbench/utils/keyUtils.ts\");\n/* harmony import */ var _worksUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./worksUtils */ \"(app-pages-browser)/./app/workbench/utils/worksUtils.ts\");\n/* harmony import */ var _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./classProjectsUtils */ \"(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./app/workbench/utils/types.ts\");\n// 工作台通用工具函数导出\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGNBQWM7QUFDYztBQUNHO0FBQ0M7QUFDRjtBQUNEO0FBQ087QUFDUjtBQUNEO0FBQ0U7QUFDUTtBQVFiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvaW5kZXgudHM/YjYxMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDlt6XkvZzlj7DpgJrnlKjlt6Xlhbflh73mlbDlr7zlh7pcbmV4cG9ydCAqIGZyb20gJy4vYmFzZVV0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vc3R1ZGVudFV0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vdGVtcGxhdGVVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3BvaW50c1V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vY2xhc3NVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL25vdGlmaWNhdGlvblV0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vdGFza1V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4va2V5VXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi93b3Jrc1V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vY2xhc3NQcm9qZWN0c1V0aWxzJztcblxuLy8g5a+85Ye65qih5p2/566h55CG55u45YWz57G75Z6LXG5leHBvcnQgdHlwZSB7IFRlbXBsYXRlLCBGb2xkZXIsIFRlbXBsYXRlTW9kYWxTdGF0ZSwgVGVtcGxhdGVQaWNrZXJTdGF0ZSB9IGZyb20gJy4vdGVtcGxhdGVVdGlscyc7XG5leHBvcnQgdHlwZSB7IFN0dWRlbnQgfSBmcm9tICcuL2NsYXNzVXRpbHMnO1xuZXhwb3J0IHR5cGUgeyBXb3Jrc1N0YXRlLCBEcmFnU3RhdGUgfSBmcm9tICcuL3dvcmtzVXRpbHMnO1xuZXhwb3J0IHR5cGUgeyBUZW1wbGF0ZVRhc2tEYXRhLCBUZW1wbGF0ZU1vZGFsRGF0YSB9IGZyb20gJy4vdGFza1V0aWxzJztcbmV4cG9ydCB0eXBlIHsgUHJvamVjdCwgQ2xhc3NQcm9qZWN0c1N0YXRlIH0gZnJvbSAnLi9jbGFzc1Byb2plY3RzVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/index.ts\n"));

/***/ })

});