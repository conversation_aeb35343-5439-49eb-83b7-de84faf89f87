"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classUtils.ts":
/*!*******************************************!*\
  !*** ./app/workbench/utils/classUtils.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSchoolsCache: function() { return /* binding */ clearSchoolsCache; },\n/* harmony export */   clearTeacherClassCache: function() { return /* binding */ clearTeacherClassCache; },\n/* harmony export */   debugClassCache: function() { return /* binding */ debugClassCache; },\n/* harmony export */   deleteClass: function() { return /* binding */ deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* binding */ deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* binding */ editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* binding */ exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* binding */ exportStudentsViaAPI; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* binding */ fetchClassStudentsWithNotification; },\n/* harmony export */   fetchStudentPoints: function() { return /* binding */ fetchStudentPoints; },\n/* harmony export */   fetchUserSchools: function() { return /* binding */ fetchUserSchools; },\n/* harmony export */   generateClassInviteCode: function() { return /* binding */ generateClassInviteCode; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* binding */ getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* binding */ getMinAvailablePoints; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* binding */ getSchoolsCacheInfo; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* binding */ getTeacherClassCacheInfo; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* binding */ handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* binding */ handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* binding */ handleSchoolSelection; },\n/* harmony export */   initSchoolModalState: function() { return /* binding */ initSchoolModalState; },\n/* harmony export */   preloadSchoolsData: function() { return /* binding */ preloadSchoolsData; },\n/* harmony export */   removeAssistantTeacher: function() { return /* binding */ removeAssistantTeacher; },\n/* harmony export */   searchTeacherByPhone: function() { return /* binding */ searchTeacherByPhone; },\n/* harmony export */   transferClass: function() { return /* binding */ transferClass; },\n/* harmony export */   updateClassInfo: function() { return /* binding */ updateClassInfo; },\n/* harmony export */   validateClassName: function() { return /* binding */ validateClassName; }\n/* harmony export */ });\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n\n\n\n\n\n/**\n * 班级管理相关工具函数\n */ /**\n * 编辑班级信息\n * @param classInfo 班级信息\n * @param values 更新的值\n * @returns Promise<boolean>\n */ const updateClassInfo = async (classInfo, values)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClassSimple({\n            id: classInfo.id,\n            className: values.className\n        });\n        if (response.data.code === 200) {\n            notification.success(\"班级信息更新成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"更新班级信息失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"更新班级信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"更新班级信息失败\");\n        return false;\n    }\n};\n/**\n * 删除班级\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const deleteClass = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n        if (response.data.code === 200) {\n            notification.success(\"班级删除成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"删除班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"删除班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"删除班级失败\");\n        return false;\n    }\n};\n/**\n * 转移班级\n * @param classId 班级ID\n * @param newTeacherId 新教师ID\n * @param transferType 转移类型\n * @returns Promise<boolean>\n */ const transferClass = async (classId, newTeacherId, transferType)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.transferClass(classId, newTeacherId, transferType);\n        if (response.data.code === 200) {\n            notification.success(\"班级转移成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"转移班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"转移班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转移班级失败\");\n        return false;\n    }\n};\n/**\n * 生成班级邀请码\n * @param classId 班级ID\n * @returns Promise<string | null>\n */ const generateClassInviteCode = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.generateInviteCode(classId);\n        if (response.data.code === 200) {\n            const inviteCode = response.data.data.inviteCode;\n            notification.success(\"邀请码生成成功\");\n            return inviteCode;\n        } else {\n            notification.error(response.data.message || \"生成邀请码失败\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"生成邀请码失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"生成邀请码失败\");\n        return null;\n    }\n};\n/**\n * 搜索教师\n * @param phone 教师手机号\n * @returns Promise<any | null>\n */ const searchTeacherByPhone = async (phone)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.searchTeacherByPhone(phone);\n        if (response.data.code === 200) {\n            return response.data.data;\n        } else {\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n            notification.error(response.data.message || \"未找到该教师\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"搜索教师失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        return null;\n    }\n};\n/**\n * 导出学生信息\n * @param students 学生列表\n * @param className 班级名称\n */ const exportStudentsData = (students, className)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        if (students.length === 0) {\n            notification.warning(\"没有学生数据可以导出\");\n            return;\n        }\n        // 准备导出数据\n        const exportData = students.map((student, index)=>{\n            var _student_currentTemplate;\n            return {\n                序号: index + 1,\n                学号: student.studentNumber || \"\",\n                姓名: student.nickName || \"\",\n                总积分: student.totalPoints || 0,\n                可用积分: student.availablePoints || 0,\n                当前模板: ((_student_currentTemplate = student.currentTemplate) === null || _student_currentTemplate === void 0 ? void 0 : _student_currentTemplate.templateName) || \"无\"\n            };\n        });\n        // 转换为CSV格式\n        const headers = Object.keys(exportData[0]);\n        const csvContent = [\n            headers.join(\",\"),\n            ...exportData.map((row)=>headers.map((header)=>'\"'.concat(row[header], '\"')).join(\",\"))\n        ].join(\"\\n\");\n        // 创建下载链接\n        const blob = new Blob([\n            \"\\uFEFF\" + csvContent\n        ], {\n            type: \"text/csv;charset=utf-8;\"\n        });\n        const link = document.createElement(\"a\");\n        const url = URL.createObjectURL(blob);\n        link.setAttribute(\"href\", url);\n        link.setAttribute(\"download\", \"\".concat(className, \"_学生信息_\").concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        link.style.visibility = \"hidden\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        notification.success(\"学生信息导出成功\");\n    } catch (error) {\n        console.error(\"导出学生信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(\"导出学生信息失败\");\n    }\n};\n/**\n * 使用API导出学生信息\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const exportStudentsViaAPI = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const hideLoading = notification.loading(\"正在导出学生信息...\");\n        const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_2__.studentApi.exportStudents(classId);\n        if (hideLoading) {\n            hideLoading.close();\n        }\n        if (response.data.code === 200) {\n            notification.success(\"导出学生成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"导出学生失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"导出学生失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"导出学生失败，请稍后重试\");\n        return false;\n    }\n};\n/**\n * 验证班级名称\n * @param className 班级名称\n * @returns { isValid: boolean; message?: string }\n */ const validateClassName = (className)=>{\n    if (!className || className.trim().length === 0) {\n        return {\n            isValid: false,\n            message: \"班级名称不能为空\"\n        };\n    }\n    if (className.length > 50) {\n        return {\n            isValid: false,\n            message: \"班级名称不能超过50个字符\"\n        };\n    }\n    // 检查特殊字符\n    const invalidChars = /[<>:\"/\\\\|?*]/;\n    if (invalidChars.test(className)) {\n        return {\n            isValid: false,\n            message: '班级名称不能包含特殊字符 < > : \" / \\\\ | ? *'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * 编辑班级信息（增强版）\n * @param classId 班级ID\n * @param values 班级信息\n * @returns Promise<{ success: boolean; data?: any }>\n */ const editClassInfo = async (classId, values)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        console.log(\"开始编辑班级:\", {\n            classId: classId,\n            values: values\n        });\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            className: values.className,\n            grade: values.grade || \"\"\n        });\n        console.log(\"编辑班级API响应:\", response);\n        if (response.data.code === 200) {\n            console.log(\"编辑班级成功\");\n            notification.success(\"编辑班级成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            console.error(\"编辑班级失败 - API返回错误:\", {\n                code: response.data.code,\n                message: response.data.message,\n                data: response.data\n            });\n            notification.error(response.data.message || \"编辑班级失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"编辑班级失败 - 请求异常:\", {\n            error: error,\n            message: error.message,\n            response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n            status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n        });\n        notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 移出协助教师\n * @param classId 班级ID\n * @returns Promise<{ success: boolean; data?: any }>\n */ const removeAssistantTeacher = async (classId)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            assistantTeacherId: 0\n        });\n        if (response.data.code === 200) {\n            notification.success(\"移出协助教师成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            notification.error(response.data.message || \"移出协助教师失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"移出协助教师失败:\", error);\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 删除班级（增强版，包含学生检查）\n * @param classId 班级ID\n * @param students 学生列表\n * @param onSuccess 成功回调\n * @returns Promise<boolean>\n */ const deleteClassWithCheck = async (classId, students, onSuccess)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    // 先检查是否有学生\n    if (students.length > 0) {\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning({\n            title: \"无法删除班级\",\n            content: \"该班级还有 \".concat(students.length, \" 名学生，请先移出所有学生后再删除班级。\"),\n            okText: \"确定\"\n        });\n        return false;\n    }\n    return new Promise((resolve)=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].confirm({\n            title: \"确认删除班级\",\n            content: \"删除后无法恢复，确定要删除这个班级吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        resolve(true);\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                        resolve(false);\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                    resolve(false);\n                }\n            },\n            onCancel: ()=>{\n                resolve(false);\n            }\n        });\n    });\n};\n// 学校数据缓存\nlet schoolsCache = null;\nconst SCHOOL_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\n/**\n * 获取用户关联的学校列表（支持缓存）\n * @param useCache 是否使用缓存，默认true\n * @returns Promise<School[]>\n */ const fetchUserSchools = async function() {\n    let useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    try {\n        // 检查缓存\n        if (useCache && schoolsCache && Date.now() - schoolsCache.timestamp < SCHOOL_CACHE_DURATION) {\n            console.log(\"使用学校列表缓存\");\n            return schoolsCache.data;\n        }\n        console.log(\"=== 开始获取用户学校列表 ===\");\n        // 从localStorage获取用户信息\n        const userInfo = localStorage.getItem(\"user\");\n        if (!userInfo) {\n            console.error(\"未找到用户信息，请检查登录状态\");\n            throw new Error(\"用户未登录\");\n        }\n        const user = JSON.parse(userInfo);\n        console.log(\"解析的用户信息:\", user);\n        // 根据项目中其他组件的使用方式，尝试多种可能的用户ID字段名\n        const userId = user.id || user.userId || user.teacherId;\n        if (!userId) {\n            console.error(\"用户信息中未找到ID字段:\", user);\n            throw new Error(\"用户ID不存在\");\n        }\n        console.log(\"使用的用户ID:\", userId);\n        // 使用项目中已有的API函数\n        const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_3__.schoolApi.getUserSchools();\n        console.log(\"获取学校数据:\", response);\n        if (response.data.code === 200 && response.data.data) {\n            // 确保数据是数组\n            const schoolsData = Array.isArray(response.data.data) ? response.data.data : [];\n            console.log(\"成功获取学校列表:\", schoolsData);\n            // 更新缓存\n            schoolsCache = {\n                data: schoolsData,\n                timestamp: Date.now()\n            };\n            return schoolsData;\n        } else {\n            console.error(\"API返回错误:\", response.data);\n            throw new Error(\"获取学校列表失败: \".concat(response.data.msg || \"未知错误\"));\n        }\n    } catch (error) {\n        console.error(\"获取学校列表失败:\", error);\n        throw error;\n    }\n};\n/**\n * 清除学校列表缓存\n */ const clearSchoolsCache = ()=>{\n    schoolsCache = null;\n    console.log(\"已清除学校列表缓存\");\n};\n/**\n * 获取学校缓存信息\n * @returns 缓存信息或null\n */ const getSchoolsCacheInfo = ()=>{\n    if (!schoolsCache) return null;\n    const isExpired = Date.now() - schoolsCache.timestamp >= SCHOOL_CACHE_DURATION;\n    return {\n        dataCount: schoolsCache.data.length,\n        timestamp: schoolsCache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : SCHOOL_CACHE_DURATION - (Date.now() - schoolsCache.timestamp)\n    };\n};\n/**\n * 预加载学校数据\n * @param delay 延迟时间（毫秒），默认1000ms\n */ const preloadSchoolsData = function() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1000;\n    setTimeout(async ()=>{\n        try {\n            await fetchUserSchools();\n            console.log(\"学校数据预加载完成\");\n        } catch (error) {\n            console.error(\"学校数据预加载失败:\", error);\n        }\n    }, delay);\n};\n// 缓存持续时间：5分钟\nconst TEACHER_CACHE_DURATION = 5 * 60 * 1000;\n// 全局缓存对象\nlet teacherClassesCache = {};\n// 已移除：fetchTeacherSchools - 请使用 classService.getTeacherSchools()\n// 已移除：fetchTeacherClasses - 请使用 classService.getSchoolClasses()\n// 已移除：updateTeacherClassCache - 请使用 classService 的缓存系统\n/**\n * 清除教师班级缓存\n * @param schoolId 可选，指定学校ID。不传则清除所有缓存\n */ const clearTeacherClassCache = (schoolId)=>{\n    if (schoolId) {\n        delete teacherClassesCache[schoolId];\n        console.log(\"已清除指定学校的教师班级缓存:\", schoolId);\n    } else {\n        teacherClassesCache = {};\n        console.log(\"已清除所有教师班级缓存\");\n    }\n};\n/**\n * 调试函数：获取当前缓存状态\n */ const debugClassCache = ()=>{\n    console.log(\"=== 班级缓存调试信息 ===\");\n    console.log(\"缓存持续时间:\", TEACHER_CACHE_DURATION, \"ms\");\n    console.log(\"当前时间:\", new Date().toISOString());\n    console.log(\"缓存内容:\");\n    Object.keys(teacherClassesCache).forEach((schoolId)=>{\n        const cache = teacherClassesCache[Number(schoolId)];\n        const isExpired = Date.now() - cache.timestamp > TEACHER_CACHE_DURATION;\n        console.log(\"  学校\".concat(schoolId, \":\"), {\n            班级数量: cache.data.length,\n            缓存时间: new Date(cache.timestamp).toISOString(),\n            是否过期: isExpired,\n            剩余时间: isExpired ? \"已过期\" : \"\".concat(Math.round((TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)) / 1000), \"秒\")\n        });\n    });\n    console.log(\"=== 调试信息结束 ===\");\n};\n/**\n * 获取教师班级缓存状态\n * @param schoolId 学校ID\n * @returns 缓存信息或null\n */ const getTeacherClassCacheInfo = (schoolId)=>{\n    const cache = teacherClassesCache[schoolId];\n    if (!cache) return null;\n    const isExpired = Date.now() - cache.timestamp >= TEACHER_CACHE_DURATION;\n    return {\n        schoolId: cache.schoolId,\n        dataCount: cache.data.length,\n        timestamp: cache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)\n    };\n};\n/**\n * 处理学校选择的业务逻辑\n * @param school 选中的学校\n * @param teacherId 教师ID\n * @param callbacks 回调函数集合\n */ const handleSchoolSelection = async (school, teacherId, callbacks)=>{\n    try {\n        var // 通知学校选择变化\n        _callbacks_onSchoolSelect, // 切换到班级管理页面\n        _callbacks_onMenuItemClick, // 通知开始加载班级数据\n        _callbacks_onClassesUpdate, // 通知班级数据更新完成\n        _callbacks_onClassesUpdate1;\n        console.log(\"处理学校选择:\", school.schoolName);\n        (_callbacks_onSchoolSelect = callbacks.onSchoolSelect) === null || _callbacks_onSchoolSelect === void 0 ? void 0 : _callbacks_onSchoolSelect.call(callbacks, school);\n        (_callbacks_onMenuItemClick = callbacks.onMenuItemClick) === null || _callbacks_onMenuItemClick === void 0 ? void 0 : _callbacks_onMenuItemClick.call(callbacks, \"班级管理\");\n        (_callbacks_onClassesUpdate = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate === void 0 ? void 0 : _callbacks_onClassesUpdate.call(callbacks, [], true, null);\n        // 获取班级列表\n        const classList = await fetchTeacherClasses(school.id, teacherId);\n        (_callbacks_onClassesUpdate1 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate1 === void 0 ? void 0 : _callbacks_onClassesUpdate1.call(callbacks, classList, false, null);\n        console.log(\"学校选择处理完成，班级数量:\", classList.length);\n    } catch (error) {\n        var _callbacks_onClassesUpdate2;\n        console.error(\"处理学校选择失败:\", error);\n        (_callbacks_onClassesUpdate2 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate2 === void 0 ? void 0 : _callbacks_onClassesUpdate2.call(callbacks, [], false, error.message || \"获取班级列表失败\");\n    }\n};\n/**\n * 初始化学校选择模态框状态\n * @returns SchoolModalState\n */ const initSchoolModalState = ()=>({\n        schools: [],\n        loading: false,\n        error: null,\n        mounted: false\n    });\n/**\n * 处理学校选择模态框的数据加载\n * @param userId 用户ID\n * @param setState 状态更新函数\n * @param useCache 是否使用缓存\n */ const handleSchoolModalDataLoad = async function(userId, setState) {\n    let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    if (!userId) {\n        setState({\n            error: \"用户未登录\"\n        });\n        return;\n    }\n    try {\n        setState({\n            loading: true,\n            error: null\n        });\n        const schoolsData = await fetchUserSchools(useCache);\n        setState({\n            schools: schoolsData,\n            loading: false\n        });\n    } catch (err) {\n        console.error(\"获取学校列表失败:\", err);\n        setState({\n            error: \"获取学校列表失败，请稍后重试\",\n            loading: false\n        });\n    }\n};\n/**\n * 处理学校选择模态框的重试逻辑\n * @param setState 状态更新函数\n * @param userId 用户ID\n */ const handleSchoolModalRetry = async (setState, userId)=>{\n    await handleSchoolModalDataLoad(userId, setState, false);\n};\n/**\n * 获取班级学生列表（带通知）\n * @param classId 班级ID\n * @param notification 通知组件\n * @returns Promise<Student[]>\n */ const fetchClassStudentsWithNotification = async (classId, notification)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getClassStudents(classId);\n        if (response.data.code === 200) {\n            return response.data.data || [];\n        } else {\n            console.error(\"获取学生列表失败:\", response.data.message);\n            notification.error(\"获取学生列表失败\");\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取学生列表失败:\", error);\n        notification.error(\"获取学生列表失败\");\n        return [];\n    }\n};\n/**\n * 获取学生能量信息（优化版本）\n * @param userIds 用户ID数组\n * @param notification 通知组件\n * @returns Promise<Map<number, number>>\n */ const fetchStudentPoints = async (userIds, notification)=>{\n    if (userIds.length === 0) {\n        return new Map();\n    }\n    try {\n        const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n        console.log(\"开始获取学生能量信息，userIds:\", userIds);\n        console.log(\"使用API端点:\", \"\".concat(pointsApi.basePointUrl, \"/batch-total\"));\n        // 设置3秒超时，避免长时间等待影响用户体验\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"请求超时\")), 3000);\n        });\n        const apiPromise = pointsApi.getBatchStudentPoints(userIds);\n        const response = await Promise.race([\n            apiPromise,\n            timeoutPromise\n        ]);\n        const pointsMap = new Map();\n        console.log(\"批量积分响应\", response);\n        if (response.data.code === 200) {\n            const data = response.data.data;\n            // 遍历返回的学生积分数据\n            for(const userId in data){\n                const studentData = data[userId];\n                if (studentData) {\n                    const totalPoints = studentData.totalPoints || 0;\n                    const availablePoints = studentData.availablePoints || 0;\n                    // 修正计算逻辑：与 assign-points-modal.tsx 保持一致\n                    // totalPoints: 总积分, availablePoints: 已使用积分\n                    // 可分配积分 = 总积分 - 已使用积分\n                    const remainingPoints = totalPoints - availablePoints;\n                    pointsMap.set(Number(userId), Math.max(0, remainingPoints));\n                } else {\n                    pointsMap.set(Number(userId), 0);\n                }\n            }\n            // 确保所有请求的用户都有数据\n            userIds.forEach((uid)=>{\n                if (!pointsMap.has(uid)) {\n                    pointsMap.set(uid, 0);\n                }\n            });\n            return pointsMap;\n        } else {\n            // API返回错误，设置默认值但不显示错误提示（避免干扰用户体验）\n            userIds.forEach((uid)=>pointsMap.set(uid, 0));\n            console.warn(\"获取学生能量信息失败，使用默认值:\", response.data.message);\n            return pointsMap;\n        }\n    } catch (error) {\n        var _error_response, _error_response1;\n        console.error(\"批量获取学生能量失败:\", error);\n        // 设置默认值，让用户可以继续操作\n        const pointsMap = new Map();\n        userIds.forEach((uid)=>pointsMap.set(uid, 0));\n        // 根据错误类型决定是否显示提示\n        if (error.message === \"请求超时\") {\n            console.warn(\"获取能量信息超时，使用默认值，用户可继续操作\");\n        // 不显示错误提示，避免干扰用户体验\n        } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n            console.warn(\"服务器错误，使用默认能量值\");\n        // 服务器错误时也不显示提示，让用户可以继续操作\n        } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n            // 权限问题可能需要用户知道\n            notification.warning(\"暂无法获取能量信息，已设置默认值\");\n        }\n        // 其他错误也不显示提示，保持良好的用户体验\n        return pointsMap;\n    }\n};\n/**\n * 计算所有学生的最低可分配能量\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 最低可分配能量\n */ const getMinAvailablePoints = (selectedStudents, studentPointsMap)=>{\n    if (selectedStudents.length === 0) return 0;\n    const selectedStudentPoints = selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n    return Math.min(...selectedStudentPoints);\n};\n/**\n * 获取当前分配方式的提示信息\n * @param selectedDistribution 选中的分配方式\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 提示信息对象\n */ const getEnergyDisplayInfo = (selectedDistribution, selectedStudents, studentPointsMap)=>{\n    if (selectedDistribution === \"assign\" || selectedDistribution === \"distribute\") {\n        return {\n            label: \"最低可分配能量\",\n            value: getMinAvailablePoints(selectedStudents, studentPointsMap)\n        };\n    }\n    return {\n        label: \"\",\n        value: 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/utils/index.ts":
/*!**************************************!*\
  !*** ./app/workbench/utils/index.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetNotification: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.GetNotification; },\n/* harmony export */   addStudentToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.addStudentToClass; },\n/* harmony export */   applyTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.applyTemplate; },\n/* harmony export */   assignPointsToStudent: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.assignPointsToStudent; },\n/* harmony export */   assignTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.assignTemplateToStudents; },\n/* harmony export */   batchAssignPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchAssignPoints; },\n/* harmony export */   batchUseKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchUseKeys; },\n/* harmony export */   calculateCompletionRate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateCompletionRate; },\n/* harmony export */   calculateQuickTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateQuickTime; },\n/* harmony export */   calculateTotalAvailablePoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.calculateTotalAvailablePoints; },\n/* harmony export */   clearSchoolsCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.clearSchoolsCache; },\n/* harmony export */   clearTeacherClassCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.clearTeacherClassCache; },\n/* harmony export */   debounce: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.debounce; },\n/* harmony export */   debugClassCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.debugClassCache; },\n/* harmony export */   deepClone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.deepClone; },\n/* harmony export */   deleteClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsViaAPI; },\n/* harmony export */   fetchAllClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchAllClassProjects; },\n/* harmony export */   fetchBatchStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchBatchStudentPoints; },\n/* harmony export */   fetchClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchClassProjects; },\n/* harmony export */   fetchClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchClassStudents; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchClassStudentsWithNotification; },\n/* harmony export */   fetchClassTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchClassTasks; },\n/* harmony export */   fetchCompleteClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchCompleteClassStudents; },\n/* harmony export */   fetchCurrentTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchCurrentTemplate; },\n/* harmony export */   fetchFolderTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolderTemplates; },\n/* harmony export */   fetchFolders: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolders; },\n/* harmony export */   fetchKeyRecords: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.fetchKeyRecords; },\n/* harmony export */   fetchMyTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchMyTemplates; },\n/* harmony export */   fetchOfficialTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchOfficialTemplates; },\n/* harmony export */   fetchSingleStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchSingleStudentPoints; },\n/* harmony export */   fetchSpecialTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchSpecialTemplate; },\n/* harmony export */   fetchStudentPoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchStudentPoints; },\n/* harmony export */   fetchStudentsInfo: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchStudentsInfo; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchTeacherClassesForProjects; },\n/* harmony export */   fetchTeacherWorks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchTeacherWorks; },\n/* harmony export */   fetchUserSchools: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchUserSchools; },\n/* harmony export */   fetchWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fetchWorks; },\n/* harmony export */   filterTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.filterTasks; },\n/* harmony export */   filterTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.filterTemplates; },\n/* harmony export */   fixImageUrl: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fixImageUrl; },\n/* harmony export */   fixProjectImageUrl: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fixProjectImageUrl; },\n/* harmony export */   formatAddressDisplay: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatAddressDisplay; },\n/* harmony export */   formatDateTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatDateTime; },\n/* harmony export */   formatFileSize: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatFileSize; },\n/* harmony export */   formatPackageInfo: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.formatPackageInfo; },\n/* harmony export */   formatPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.formatPoints; },\n/* harmony export */   formatPublishTime: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.formatPublishTime; },\n/* harmony export */   formatTaskDate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDate; },\n/* harmony export */   formatTaskDateSimple: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDateSimple; },\n/* harmony export */   formatTimeDisplay: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTimeDisplay; },\n/* harmony export */   generateClassInviteCode: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.generateClassInviteCode; },\n/* harmony export */   generateRandomString: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateRandomString; },\n/* harmony export */   generateStepIndicatorConfig: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateStepIndicatorConfig; },\n/* harmony export */   getAvatarColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarColor; },\n/* harmony export */   getAvatarGradientColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarGradientColor; },\n/* harmony export */   getBatchStudentPackageExpiries: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getBatchStudentPackageExpiries; },\n/* harmony export */   getCurrentDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getCurrentDateTime; },\n/* harmony export */   getCurrentEnergyAmount: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrentEnergyAmount; },\n/* harmony export */   getCurrentUserId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserSchoolId; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getMinAvailablePoints; },\n/* harmony export */   getNextDayDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getNextDayDateTime; },\n/* harmony export */   getRelativeTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.getRelativeTime; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getSchoolsCacheInfo; },\n/* harmony export */   getStudentPackageExpiry: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getStudentPackageExpiry; },\n/* harmony export */   getTaskRemainingTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskRemainingTime; },\n/* harmony export */   getTaskStatus: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatus; },\n/* harmony export */   getTaskStatusColor: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatusColor; },\n/* harmony export */   getTaskTimes: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTaskTimes; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getTeacherClassCacheInfo; },\n/* harmony export */   getTemplateDetails: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTemplateDetails; },\n/* harmony export */   handleDeleteTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleDeleteTask; },\n/* harmony export */   handleEditTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleEditTask; },\n/* harmony export */   handleMouseDown: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseDown; },\n/* harmony export */   handleMouseLeave: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseLeave; },\n/* harmony export */   handleMouseMove: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseMove; },\n/* harmony export */   handleMouseUp: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseUp; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolSelection; },\n/* harmony export */   handleSelectWork: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleSelectWork; },\n/* harmony export */   handleViewTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleViewTask; },\n/* harmony export */   handleViewWork: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.handleViewWork; },\n/* harmony export */   handleWheelScroll: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleWheelScroll; },\n/* harmony export */   importStudentsFromFile: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsFromFile; },\n/* harmony export */   importStudentsToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsToClass; },\n/* harmony export */   initClassProjectsState: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.initClassProjectsState; },\n/* harmony export */   initDragState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initDragState; },\n/* harmony export */   initSchoolModalState: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.initSchoolModalState; },\n/* harmony export */   initTemplateModalData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateModalData; },\n/* harmony export */   initTemplateModalState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplateModalState; },\n/* harmony export */   initTemplatePickerState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplatePickerState; },\n/* harmony export */   initTemplateTaskData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateTaskData; },\n/* harmony export */   initWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initWorksState; },\n/* harmony export */   isValidEmail: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidEmail; },\n/* harmony export */   isValidKeyFormat: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.isValidKeyFormat; },\n/* harmony export */   isValidPhone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidPhone; },\n/* harmony export */   loadMoreWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.loadMoreWorks; },\n/* harmony export */   mergeStudentData: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.mergeStudentData; },\n/* harmony export */   parseExcelKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.parseExcelKeys; },\n/* harmony export */   preloadSchoolsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.preloadSchoolsData; },\n/* harmony export */   prepareBlockAssignment: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.prepareBlockAssignment; },\n/* harmony export */   publishTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.publishTask; },\n/* harmony export */   redeemKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.redeemKey; },\n/* harmony export */   removeAssistantTeacher: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.removeAssistantTeacher; },\n/* harmony export */   removeFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.removeFile; },\n/* harmony export */   removeStudentsFromClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.removeStudentsFromClass; },\n/* harmony export */   removeTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.removeTemplate; },\n/* harmony export */   resetStudentPassword: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.resetStudentPassword; },\n/* harmony export */   resetWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.resetWorksState; },\n/* harmony export */   searchTeacherByPhone: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.searchTeacherByPhone; },\n/* harmony export */   shouldShowEmptyState: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.shouldShowEmptyState; },\n/* harmony export */   showBatchOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showBatchOperationResult; },\n/* harmony export */   showConfirm: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showConfirm; },\n/* harmony export */   showError: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showError; },\n/* harmony export */   showInfo: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showInfo; },\n/* harmony export */   showLoading: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showLoading; },\n/* harmony export */   showOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showOperationResult; },\n/* harmony export */   showRedeemConfirmModal: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.showRedeemConfirmModal; },\n/* harmony export */   showSuccess: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showSuccess; },\n/* harmony export */   showWarning: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showWarning; },\n/* harmony export */   sortProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.sortProjects; },\n/* harmony export */   syncTeacherTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.syncTeacherTemplateToStudents; },\n/* harmony export */   throttle: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.throttle; },\n/* harmony export */   transferClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.transferClass; },\n/* harmony export */   triggerPointsUpdateEvent: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.triggerPointsUpdateEvent; },\n/* harmony export */   uniqueArray: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.uniqueArray; },\n/* harmony export */   updateClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.updateClassInfo; },\n/* harmony export */   updatePersonalTemplateAssignments: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updatePersonalTemplateAssignments; },\n/* harmony export */   updateStudentsTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updateStudentsTemplate; },\n/* harmony export */   uploadFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.uploadFile; },\n/* harmony export */   validateClassName: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.validateClassName; },\n/* harmony export */   validateEnergyDistribution: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateEnergyDistribution; },\n/* harmony export */   validateExcelFile: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validateExcelFile; },\n/* harmony export */   validateKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.validateKey; },\n/* harmony export */   validatePointsAssignment: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignment; },\n/* harmony export */   validatePointsAssignmentModal: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignmentModal; },\n/* harmony export */   validateTaskForm: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskForm; },\n/* harmony export */   validateTaskParams: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskParams; },\n/* harmony export */   validateUploadFiles: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateUploadFiles; }\n/* harmony export */ });\n/* harmony import */ var _baseUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baseUtils */ \"(app-pages-browser)/./app/workbench/utils/baseUtils.ts\");\n/* harmony import */ var _studentUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./studentUtils */ \"(app-pages-browser)/./app/workbench/utils/studentUtils.ts\");\n/* harmony import */ var _templateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templateUtils */ \"(app-pages-browser)/./app/workbench/utils/templateUtils.ts\");\n/* harmony import */ var _pointsUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointsUtils */ \"(app-pages-browser)/./app/workbench/utils/pointsUtils.ts\");\n/* harmony import */ var _classUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./classUtils */ \"(app-pages-browser)/./app/workbench/utils/classUtils.ts\");\n/* harmony import */ var _notificationUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notificationUtils */ \"(app-pages-browser)/./app/workbench/utils/notificationUtils.ts\");\n/* harmony import */ var _taskUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./taskUtils */ \"(app-pages-browser)/./app/workbench/utils/taskUtils.ts\");\n/* harmony import */ var _keyUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./keyUtils */ \"(app-pages-browser)/./app/workbench/utils/keyUtils.ts\");\n/* harmony import */ var _worksUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./worksUtils */ \"(app-pages-browser)/./app/workbench/utils/worksUtils.ts\");\n/* harmony import */ var _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./classProjectsUtils */ \"(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./app/workbench/utils/types.ts\");\n// 工作台通用工具函数导出\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsY0FBYztBQUNjO0FBQ0c7QUFDQztBQUNGO0FBQ0Q7QUFDTztBQUNSO0FBQ0Q7QUFDRTtBQUNRO0FBUWIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3dvcmtiZW5jaC91dGlscy9pbmRleC50cz9iNjEwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOW3peS9nOWPsOmAmueUqOW3peWFt+WHveaVsOWvvOWHulxuZXhwb3J0ICogZnJvbSAnLi9iYXNlVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9zdHVkZW50VXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi90ZW1wbGF0ZVV0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vcG9pbnRzVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9jbGFzc1V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vbm90aWZpY2F0aW9uVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi90YXNrVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9rZXlVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3dvcmtzVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9jbGFzc1Byb2plY3RzVXRpbHMnO1xuXG4vLyDlr7zlh7rmqKHmnb/nrqHnkIbnm7jlhbPnsbvlnotcbmV4cG9ydCB0eXBlIHsgVGVtcGxhdGUsIEZvbGRlciwgVGVtcGxhdGVNb2RhbFN0YXRlLCBUZW1wbGF0ZVBpY2tlclN0YXRlIH0gZnJvbSAnLi90ZW1wbGF0ZVV0aWxzJztcbmV4cG9ydCB0eXBlIHsgU3R1ZGVudCB9IGZyb20gJy4vY2xhc3NVdGlscyc7XG5leHBvcnQgdHlwZSB7IFdvcmtzU3RhdGUsIERyYWdTdGF0ZSB9IGZyb20gJy4vd29ya3NVdGlscyc7XG5leHBvcnQgdHlwZSB7IFRlbXBsYXRlVGFza0RhdGEsIFRlbXBsYXRlTW9kYWxEYXRhIH0gZnJvbSAnLi90YXNrVXRpbHMnO1xuZXhwb3J0IHR5cGUgeyBQcm9qZWN0LCBDbGFzc1Byb2plY3RzU3RhdGUgfSBmcm9tICcuL2NsYXNzUHJvamVjdHNVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/index.ts\n"));

/***/ })

});