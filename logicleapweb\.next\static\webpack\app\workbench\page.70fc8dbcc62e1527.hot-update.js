"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts":
/*!***************************************************!*\
  !*** ./app/workbench/utils/classProjectsUtils.ts ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAllClassProjects: function() { return /* binding */ fetchAllClassProjects; },\n/* harmony export */   fetchClassProjects: function() { return /* binding */ fetchClassProjects; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* binding */ fetchTeacherClassesForProjects; },\n/* harmony export */   fixProjectImageUrl: function() { return /* binding */ fixProjectImageUrl; },\n/* harmony export */   formatPublishTime: function() { return /* binding */ formatPublishTime; },\n/* harmony export */   getCurrentUserId: function() { return /* binding */ getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* binding */ getCurrentUserSchoolId; },\n/* harmony export */   handleViewWork: function() { return /* binding */ handleViewWork; },\n/* harmony export */   initClassProjectsState: function() { return /* binding */ initClassProjectsState; },\n/* harmony export */   sortProjects: function() { return /* binding */ sortProjects; }\n/* harmony export */ });\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n\n\n// 缓存持续时间：3分钟\nconst PROJECT_CACHE_DURATION = 3 * 60 * 1000;\nconst CLASS_LIST_CACHE_DURATION = 5 * 60 * 1000;\n// 全局缓存对象\nlet projectsCache = {};\nlet classListCache = {};\n/**\n * 初始化班级项目状态\n */ const initClassProjectsState = ()=>{\n    return {\n        projects: [],\n        classes: [],\n        selectedClassId: null,\n        keyword: \"\",\n        orderBy: \"newest\",\n        loading: false,\n        classesLoading: false\n    };\n};\n/**\n * 获取当前用户ID\n */ const getCurrentUserId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.userId) || null;\n    } catch (error) {\n        console.error(\"获取用户ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取当前用户的学校ID\n */ const getCurrentUserSchoolId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.schoolId) || null;\n    } catch (error) {\n        console.error(\"获取学校ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取教师的班级列表（用于班级项目）\n */ const fetchTeacherClassesForProjects = async (schoolId)=>{\n    try {\n        var _response_data;\n        const targetSchoolId = schoolId || getCurrentUserSchoolId();\n        if (!targetSchoolId) {\n            console.error(\"未找到学校ID\");\n            return [];\n        }\n        console.log(\"获取教师班级列表，学校ID:\", targetSchoolId);\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getTeacherClassesSimple(targetSchoolId);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            const classList = response.data.data || [];\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            return classList;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级列表失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级列表失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取特定班级的项目列表\n */ const fetchClassProjects = async function(classId) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        var _response_data;\n        console.log(\"获取班级项目，班级ID:\", classId, \"关键词:\", keyword);\n        const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_0__.worksApi.searchProjects(classId, keyword);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            var _response_data_data;\n            let projects = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) || [];\n            // 根据排序方式处理数据\n            projects = sortProjects(projects, orderBy);\n            console.log(\"成功获取班级项目，数量:\", projects.length);\n            return projects;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级项目失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取所有班级的项目列表\n */ const fetchAllClassProjects = async function(classes) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        console.log(\"获取所有班级项目，班级数量:\", classes.length, \"关键词:\", keyword);\n        if (classes.length === 0) {\n            return [];\n        }\n        // 并发获取所有班级的项目\n        const projectPromises = classes.map(async (classInfo)=>{\n            try {\n                const classProjects = await fetchClassProjects(classInfo.id, orderBy, keyword);\n                // 为每个项目添加班级信息\n                return classProjects.map((project)=>({\n                        ...project,\n                        classId: classInfo.id,\n                        className: classInfo.className,\n                        grade: classInfo.grade\n                    }));\n            } catch (error) {\n                console.error(\"获取班级 \".concat(classInfo.className, \" 的项目失败:\"), error);\n                return [];\n            }\n        });\n        const allProjectsArrays = await Promise.all(projectPromises);\n        const allProjects = allProjectsArrays.flat();\n        // 根据排序方式处理数据\n        const sortedProjects = sortProjects(allProjects, orderBy);\n        console.log(\"成功获取所有班级项目，总数量:\", sortedProjects.length);\n        return sortedProjects;\n    } catch (error) {\n        console.error(\"获取所有班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 项目排序函数\n */ const sortProjects = (projects, orderBy)=>{\n    const sortedProjects = [\n        ...projects\n    ];\n    switch(orderBy){\n        case \"newest\":\n            return sortedProjects.sort((a, b)=>new Date(b.publishToClassTime).getTime() - new Date(a.publishToClassTime).getTime());\n        case \"oldest\":\n            return sortedProjects.sort((a, b)=>new Date(a.publishToClassTime).getTime() - new Date(b.publishToClassTime).getTime());\n        case \"popular\":\n            return sortedProjects.sort((a, b)=>(b.viewCount || 0) - (a.viewCount || 0));\n        default:\n            return sortedProjects;\n    }\n};\n/**\n * 修复图片URL（用于班级项目）\n */ const fixProjectImageUrl = (url)=>{\n    if (!url) return \"/images/xiaoluo-default.webp\";\n    // 如果已经有协议前缀，直接返回\n    if (url.startsWith(\"http://\") || url.startsWith(\"https://\")) {\n        return url;\n    }\n    // 如果是阿里云OSS URL但缺少协议前缀，添加https://\n    if (url.includes(\"aliyuncs.com\") || url.includes(\"logicleap.oss\")) {\n        return \"https://\".concat(url);\n    }\n    // 如果是相对路径，保持原样\n    if (url.startsWith(\"/\")) {\n        return url;\n    }\n    // 其他情况，添加https://\n    return \"https://\".concat(url);\n};\n/**\n * 处理查看作品\n */ const handleViewWork = async (workId)=>{\n    try {\n        // 这里可以调用查看作品的模态框或跳转到作品详情页\n        console.log(\"查看作品:\", workId);\n        // 可以复用现有的查看作品逻辑\n        const { viewWork } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/utils/view-work-modal */ \"(app-pages-browser)/./lib/utils/view-work-modal.tsx\"));\n        await viewWork(workId);\n    } catch (error) {\n        console.error(\"查看作品失败:\", error);\n    }\n};\n/**\n * 格式化发布时间\n */ const formatPublishTime = (publishTime)=>{\n    try {\n        const date = new Date(publishTime);\n        const now = new Date();\n        const diffTime = now.getTime() - date.getTime();\n        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n        if (diffDays === 0) {\n            return \"今天\";\n        } else if (diffDays === 1) {\n            return \"昨天\";\n        } else if (diffDays < 7) {\n            return \"\".concat(diffDays, \"天前\");\n        } else {\n            return date.toLocaleDateString(\"zh-CN\", {\n                year: \"numeric\",\n                month: \"short\",\n                day: \"numeric\"\n            });\n        }\n    } catch (error) {\n        console.error(\"格式化时间失败:\", error);\n        return \"未知时间\";\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\n"));

/***/ })

});