"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 移除本地缓存管理，使用工具函数中的缓存\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            // 强制不使用缓存，确保获取最新数据\n            loadClasses(firstSchool.id, false);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        } else if (selectedSchool) {\n            // 如果已经有选中的学校，重新加载班级数据（不使用缓存）\n            console.log(\"班级管理：重新加载当前学校的班级数据\");\n            loadClasses(selectedSchool.id, false);\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表（强制不使用缓存，确保数据最新）\n        loadClasses(school.id, false);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表 - 使用工具函数\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            const schoolList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherSchools)();\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                loadClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(error.message || \"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用工具函数\n    const loadClasses = async function(schoolId) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            // 延迟重试，等待用户信息加载完成\n            setTimeout(()=>{\n                if (userInfo === null || userInfo === void 0 ? void 0 : userInfo.id) {\n                    console.log(\"用户信息已加载，重新尝试获取班级列表\");\n                    loadClasses(schoolId, useCache);\n                }\n            }, 1000);\n            return;\n        }\n        try {\n            // 通知父组件开始加载\n            onClassesUpdate && onClassesUpdate([], true, null);\n            console.log(\"开始获取班级列表，学校ID:\", schoolId, \"用户ID:\", userInfo.id);\n            const classList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherClasses)(schoolId, userInfo.id, useCache);\n            // 同时更新ClassSelectionModal的缓存\n            (0,_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__.updateClassCache)(schoolId, classList);\n            // 通知父组件数据更新\n            onClassesUpdate && onClassesUpdate(classList, false, null);\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            // 如果班级列表为空，尝试不使用缓存重新获取\n            if (classList.length === 0 && useCache) {\n                console.log(\"班级列表为空，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 500);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = error.message || \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            // 如果使用缓存失败，尝试不使用缓存重新获取\n            if (useCache) {\n                console.log(\"使用缓存获取失败，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 1000);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined),\n            userInfo.nickName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"login-prompt\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-icon-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-text\",\n                                children: \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-subtitle\",\n                                children: \"请先登录以使用完整功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_3__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});