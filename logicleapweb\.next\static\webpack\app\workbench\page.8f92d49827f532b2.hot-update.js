"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 移除本地缓存管理，使用工具函数中的缓存\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        console.log(\"=== 班级管理点击调试 ===\");\n        console.log(\"当前状态:\", {\n            selectedSchool: selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName,\n            schoolsCount: schools.length,\n            schoolsLoading,\n            userInfo: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.id) ? \"已登录\" : \"未登录\",\n            activeItem\n        });\n        // 打印缓存调试信息\n        (0,_utils__WEBPACK_IMPORTED_MODULE_5__.debugClassCache)();\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            // 强制不使用缓存，确保获取最新数据\n            loadClasses(firstSchool.id, false);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        } else if (selectedSchool) {\n            // 如果已经有选中的学校，重新加载班级数据（不使用缓存）\n            console.log(\"班级管理：重新加载当前学校的班级数据\");\n            loadClasses(selectedSchool.id, false);\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表（强制不使用缓存，确保数据最新）\n        loadClasses(school.id, false);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表 - 使用工具函数\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            const schoolList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherSchools)();\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                loadClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(error.message || \"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用工具函数\n    const loadClasses = async function(schoolId) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            // 延迟重试，等待用户信息加载完成\n            setTimeout(()=>{\n                if (userInfo === null || userInfo === void 0 ? void 0 : userInfo.id) {\n                    console.log(\"用户信息已加载，重新尝试获取班级列表\");\n                    loadClasses(schoolId, useCache);\n                }\n            }, 1000);\n            return;\n        }\n        try {\n            // 通知父组件开始加载\n            onClassesUpdate && onClassesUpdate([], true, null);\n            console.log(\"开始获取班级列表，学校ID:\", schoolId, \"用户ID:\", userInfo.id);\n            const classList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherClasses)(schoolId, userInfo.id, useCache);\n            // 同时更新ClassSelectionModal的缓存\n            (0,_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__.updateClassCache)(schoolId, classList);\n            // 通知父组件数据更新\n            onClassesUpdate && onClassesUpdate(classList, false, null);\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            // 如果班级列表为空，尝试不使用缓存重新获取\n            if (classList.length === 0 && useCache) {\n                console.log(\"班级列表为空，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 500);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = error.message || \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            // 如果使用缓存失败，尝试不使用缓存重新获取\n            if (useCache) {\n                console.log(\"使用缓存获取失败，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 1000);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, undefined),\n            userInfo.nickName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"login-prompt\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-icon-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-text\",\n                                children: \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-subtitle\",\n                                children: \"请先登录以使用完整功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_3__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});