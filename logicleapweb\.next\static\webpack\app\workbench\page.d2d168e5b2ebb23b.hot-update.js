"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts":
/*!***************************************************!*\
  !*** ./app/workbench/utils/classProjectsUtils.ts ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAllClassProjects: function() { return /* binding */ fetchAllClassProjects; },\n/* harmony export */   fetchClassProjects: function() { return /* binding */ fetchClassProjects; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* binding */ fetchTeacherClassesForProjects; },\n/* harmony export */   fixProjectImageUrl: function() { return /* binding */ fixProjectImageUrl; },\n/* harmony export */   formatPublishTime: function() { return /* binding */ formatPublishTime; },\n/* harmony export */   getCurrentUserId: function() { return /* binding */ getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* binding */ getCurrentUserSchoolId; },\n/* harmony export */   handleViewWork: function() { return /* binding */ handleViewWork; },\n/* harmony export */   initClassProjectsState: function() { return /* binding */ initClassProjectsState; },\n/* harmony export */   sortProjects: function() { return /* binding */ sortProjects; }\n/* harmony export */ });\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n\n\n/**\n * 初始化班级项目状态\n */ const initClassProjectsState = ()=>{\n    return {\n        projects: [],\n        classes: [],\n        selectedClassId: null,\n        keyword: \"\",\n        orderBy: \"newest\",\n        loading: false,\n        classesLoading: false\n    };\n};\n/**\n * 获取当前用户ID\n */ const getCurrentUserId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.userId) || null;\n    } catch (error) {\n        console.error(\"获取用户ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取当前用户的学校ID\n */ const getCurrentUserSchoolId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.schoolId) || null;\n    } catch (error) {\n        console.error(\"获取学校ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取教师的班级列表（用于班级项目）\n */ const fetchTeacherClassesForProjects = async (schoolId)=>{\n    try {\n        var _response_data;\n        const targetSchoolId = schoolId || getCurrentUserSchoolId();\n        if (!targetSchoolId) {\n            console.error(\"未找到学校ID\");\n            return [];\n        }\n        console.log(\"获取教师班级列表，学校ID:\", targetSchoolId);\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getTeacherClassesSimple(targetSchoolId);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            const classList = response.data.data || [];\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            return classList;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级列表失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级列表失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取特定班级的项目列表\n */ const fetchClassProjects = async function(classId) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        var _response_data;\n        console.log(\"获取班级项目，班级ID:\", classId, \"关键词:\", keyword);\n        const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_0__.worksApi.searchProjects(classId, keyword);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            var _response_data_data;\n            let projects = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) || [];\n            // 根据排序方式处理数据\n            projects = sortProjects(projects, orderBy);\n            console.log(\"成功获取班级项目，数量:\", projects.length);\n            return projects;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级项目失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取所有班级的项目列表\n */ const fetchAllClassProjects = async function(classes) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        console.log(\"获取所有班级项目，班级数量:\", classes.length, \"关键词:\", keyword);\n        if (classes.length === 0) {\n            return [];\n        }\n        // 并发获取所有班级的项目\n        const projectPromises = classes.map(async (classInfo)=>{\n            try {\n                const classProjects = await fetchClassProjects(classInfo.id, orderBy, keyword);\n                // 为每个项目添加班级信息\n                return classProjects.map((project)=>({\n                        ...project,\n                        classId: classInfo.id,\n                        className: classInfo.className,\n                        grade: classInfo.grade\n                    }));\n            } catch (error) {\n                console.error(\"获取班级 \".concat(classInfo.className, \" 的项目失败:\"), error);\n                return [];\n            }\n        });\n        const allProjectsArrays = await Promise.all(projectPromises);\n        const allProjects = allProjectsArrays.flat();\n        // 根据排序方式处理数据\n        const sortedProjects = sortProjects(allProjects, orderBy);\n        console.log(\"成功获取所有班级项目，总数量:\", sortedProjects.length);\n        return sortedProjects;\n    } catch (error) {\n        console.error(\"获取所有班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 项目排序函数\n */ const sortProjects = (projects, orderBy)=>{\n    const sortedProjects = [\n        ...projects\n    ];\n    switch(orderBy){\n        case \"newest\":\n            return sortedProjects.sort((a, b)=>new Date(b.publishToClassTime).getTime() - new Date(a.publishToClassTime).getTime());\n        case \"oldest\":\n            return sortedProjects.sort((a, b)=>new Date(a.publishToClassTime).getTime() - new Date(b.publishToClassTime).getTime());\n        case \"popular\":\n            return sortedProjects.sort((a, b)=>(b.viewCount || 0) - (a.viewCount || 0));\n        default:\n            return sortedProjects;\n    }\n};\n/**\n * 修复图片URL（用于班级项目）\n */ const fixProjectImageUrl = (url)=>{\n    if (!url) return \"/images/xiaoluo-default.webp\";\n    // 如果已经有协议前缀，直接返回\n    if (url.startsWith(\"http://\") || url.startsWith(\"https://\")) {\n        return url;\n    }\n    // 如果是阿里云OSS URL但缺少协议前缀，添加https://\n    if (url.includes(\"aliyuncs.com\") || url.includes(\"logicleap.oss\")) {\n        return \"https://\".concat(url);\n    }\n    // 如果是相对路径，保持原样\n    if (url.startsWith(\"/\")) {\n        return url;\n    }\n    // 其他情况，添加https://\n    return \"https://\".concat(url);\n};\n/**\n * 处理查看作品\n */ const handleViewWork = async (workId)=>{\n    try {\n        // 这里可以调用查看作品的模态框或跳转到作品详情页\n        console.log(\"查看作品:\", workId);\n        // 可以复用现有的查看作品逻辑\n        const { viewWork } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/utils/view-work-modal */ \"(app-pages-browser)/./lib/utils/view-work-modal.tsx\"));\n        viewWork({\n            workId\n        });\n    } catch (error) {\n        console.error(\"查看作品失败:\", error);\n    }\n};\n/**\n * 格式化发布时间\n */ const formatPublishTime = (publishTime)=>{\n    try {\n        const date = new Date(publishTime);\n        const now = new Date();\n        const diffTime = now.getTime() - date.getTime();\n        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n        if (diffDays === 0) {\n            return \"今天\";\n        } else if (diffDays === 1) {\n            return \"昨天\";\n        } else if (diffDays < 7) {\n            return \"\".concat(diffDays, \"天前\");\n        } else {\n            return date.toLocaleDateString(\"zh-CN\", {\n                year: \"numeric\",\n                month: \"short\",\n                day: \"numeric\"\n            });\n        }\n    } catch (error) {\n        console.error(\"格式化时间失败:\", error);\n        return \"未知时间\";\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\n"));

/***/ })

});