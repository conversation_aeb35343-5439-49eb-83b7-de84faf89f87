"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts":
/*!***************************************************!*\
  !*** ./app/workbench/utils/classProjectsUtils.ts ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAllClassProjects: function() { return /* binding */ fetchAllClassProjects; },\n/* harmony export */   fetchClassProjects: function() { return /* binding */ fetchClassProjects; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* binding */ fetchTeacherClassesForProjects; },\n/* harmony export */   fixProjectImageUrl: function() { return /* binding */ fixProjectImageUrl; },\n/* harmony export */   formatPublishTime: function() { return /* binding */ formatPublishTime; },\n/* harmony export */   getCurrentUserId: function() { return /* binding */ getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* binding */ getCurrentUserSchoolId; },\n/* harmony export */   handleViewWork: function() { return /* binding */ handleViewWork; },\n/* harmony export */   initClassProjectsState: function() { return /* binding */ initClassProjectsState; },\n/* harmony export */   sortProjects: function() { return /* binding */ sortProjects; }\n/* harmony export */ });\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n\n\n/**\n * 初始化班级项目状态\n */ const initClassProjectsState = ()=>{\n    return {\n        projects: [],\n        classes: [],\n        selectedClassId: null,\n        keyword: \"\",\n        orderBy: \"newest\",\n        loading: false,\n        classesLoading: false\n    };\n};\n/**\n * 获取当前用户ID\n */ const getCurrentUserId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.userId) || null;\n    } catch (error) {\n        console.error(\"获取用户ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取当前用户的学校ID\n */ const getCurrentUserSchoolId = ()=>{\n    try {\n        const userData = localStorage.getItem(\"user\");\n        const user = userData ? JSON.parse(userData) : null;\n        return (user === null || user === void 0 ? void 0 : user.schoolId) || null;\n    } catch (error) {\n        console.error(\"获取学校ID失败:\", error);\n        return null;\n    }\n};\n/**\n * 获取教师的班级列表（用于班级项目）\n */ const fetchTeacherClassesForProjects = async (schoolId)=>{\n    try {\n        var _response_data;\n        const targetSchoolId = schoolId || getCurrentUserSchoolId();\n        if (!targetSchoolId) {\n            console.error(\"未找到学校ID\");\n            return [];\n        }\n        console.log(\"获取教师班级列表，学校ID:\", targetSchoolId);\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getTeacherClassesSimple(targetSchoolId);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            const classList = response.data.data || [];\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            return classList;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级列表失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级列表失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取特定班级的项目列表\n */ const fetchClassProjects = async function(classId) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        var _response_data;\n        console.log(\"获取班级项目，班级ID:\", classId, \"关键词:\", keyword);\n        const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_0__.worksApi.searchProjects(classId, keyword);\n        if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n            var _response_data_data;\n            let projects = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) || [];\n            // 根据排序方式处理数据\n            projects = sortProjects(projects, orderBy);\n            console.log(\"成功获取班级项目，数量:\", projects.length);\n            return projects;\n        } else {\n            var _response_data1;\n            console.error(\"获取班级项目失败:\", (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 获取所有班级的项目列表\n */ const fetchAllClassProjects = async function(classes) {\n    let orderBy = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"newest\", keyword = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        console.log(\"获取所有班级项目，班级数量:\", classes.length, \"关键词:\", keyword);\n        if (classes.length === 0) {\n            return [];\n        }\n        // 并发获取所有班级的项目\n        const projectPromises = classes.map(async (classInfo)=>{\n            try {\n                const classProjects = await fetchClassProjects(classInfo.id, orderBy, keyword);\n                // 为每个项目添加班级信息\n                return classProjects.map((project)=>({\n                        ...project,\n                        classId: classInfo.id,\n                        className: classInfo.className,\n                        grade: classInfo.grade\n                    }));\n            } catch (error) {\n                console.error(\"获取班级 \".concat(classInfo.className, \" 的项目失败:\"), error);\n                return [];\n            }\n        });\n        const allProjectsArrays = await Promise.all(projectPromises);\n        const allProjects = allProjectsArrays.flat();\n        // 根据排序方式处理数据\n        const sortedProjects = sortProjects(allProjects, orderBy);\n        console.log(\"成功获取所有班级项目，总数量:\", sortedProjects.length);\n        return sortedProjects;\n    } catch (error) {\n        console.error(\"获取所有班级项目失败:\", error);\n        return [];\n    }\n};\n/**\n * 项目排序函数\n */ const sortProjects = (projects, orderBy)=>{\n    const sortedProjects = [\n        ...projects\n    ];\n    switch(orderBy){\n        case \"newest\":\n            return sortedProjects.sort((a, b)=>new Date(b.publishToClassTime).getTime() - new Date(a.publishToClassTime).getTime());\n        case \"oldest\":\n            return sortedProjects.sort((a, b)=>new Date(a.publishToClassTime).getTime() - new Date(b.publishToClassTime).getTime());\n        case \"popular\":\n            return sortedProjects.sort((a, b)=>(b.viewCount || 0) - (a.viewCount || 0));\n        default:\n            return sortedProjects;\n    }\n};\n/**\n * 修复图片URL（用于班级项目）\n */ const fixProjectImageUrl = (url)=>{\n    if (!url) return \"/images/xiaoluo-default.webp\";\n    // 如果已经有协议前缀，直接返回\n    if (url.startsWith(\"http://\") || url.startsWith(\"https://\")) {\n        return url;\n    }\n    // 如果是阿里云OSS URL但缺少协议前缀，添加https://\n    if (url.includes(\"aliyuncs.com\") || url.includes(\"logicleap.oss\")) {\n        return \"https://\".concat(url);\n    }\n    // 如果是相对路径，保持原样\n    if (url.startsWith(\"/\")) {\n        return url;\n    }\n    // 其他情况，添加https://\n    return \"https://\".concat(url);\n};\n/**\n * 处理查看作品\n */ const handleViewWork = async (workId)=>{\n    try {\n        // 这里可以调用查看作品的模态框或跳转到作品详情页\n        console.log(\"查看作品:\", workId);\n        // 可以复用现有的查看作品逻辑\n        const { viewWork } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/utils/view-work-modal */ \"(app-pages-browser)/./lib/utils/view-work-modal.tsx\"));\n        await viewWork(workId);\n    } catch (error) {\n        console.error(\"查看作品失败:\", error);\n    }\n};\n/**\n * 格式化发布时间\n */ const formatPublishTime = (publishTime)=>{\n    try {\n        const date = new Date(publishTime);\n        const now = new Date();\n        const diffTime = now.getTime() - date.getTime();\n        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n        if (diffDays === 0) {\n            return \"今天\";\n        } else if (diffDays === 1) {\n            return \"昨天\";\n        } else if (diffDays < 7) {\n            return \"\".concat(diffDays, \"天前\");\n        } else {\n            return date.toLocaleDateString(\"zh-CN\", {\n                year: \"numeric\",\n                month: \"short\",\n                day: \"numeric\"\n            });\n        }\n    } catch (error) {\n        console.error(\"格式化时间失败:\", error);\n        return \"未知时间\";\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\n"));

/***/ })

});