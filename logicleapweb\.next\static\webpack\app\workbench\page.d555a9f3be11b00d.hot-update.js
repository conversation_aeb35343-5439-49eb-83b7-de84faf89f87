"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/utils/classUtils.ts":
/*!*******************************************!*\
  !*** ./app/workbench/utils/classUtils.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSchoolsCache: function() { return /* binding */ clearSchoolsCache; },\n/* harmony export */   debugClassCache: function() { return /* binding */ debugClassCache; },\n/* harmony export */   deleteClass: function() { return /* binding */ deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* binding */ deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* binding */ editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* binding */ exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* binding */ exportStudentsViaAPI; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* binding */ fetchClassStudentsWithNotification; },\n/* harmony export */   fetchStudentPoints: function() { return /* binding */ fetchStudentPoints; },\n/* harmony export */   fetchUserSchools: function() { return /* binding */ fetchUserSchools; },\n/* harmony export */   generateClassInviteCode: function() { return /* binding */ generateClassInviteCode; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* binding */ getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* binding */ getMinAvailablePoints; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* binding */ getSchoolsCacheInfo; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* binding */ getTeacherClassCacheInfo; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* binding */ handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* binding */ handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* binding */ handleSchoolSelection; },\n/* harmony export */   initSchoolModalState: function() { return /* binding */ initSchoolModalState; },\n/* harmony export */   preloadSchoolsData: function() { return /* binding */ preloadSchoolsData; },\n/* harmony export */   removeAssistantTeacher: function() { return /* binding */ removeAssistantTeacher; },\n/* harmony export */   searchTeacherByPhone: function() { return /* binding */ searchTeacherByPhone; },\n/* harmony export */   transferClass: function() { return /* binding */ transferClass; },\n/* harmony export */   updateClassInfo: function() { return /* binding */ updateClassInfo; },\n/* harmony export */   validateClassName: function() { return /* binding */ validateClassName; }\n/* harmony export */ });\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n\n\n\n\n\n/**\n * 班级管理相关工具函数\n */ /**\n * 编辑班级信息\n * @param classInfo 班级信息\n * @param values 更新的值\n * @returns Promise<boolean>\n */ const updateClassInfo = async (classInfo, values)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClassSimple({\n            id: classInfo.id,\n            className: values.className\n        });\n        if (response.data.code === 200) {\n            notification.success(\"班级信息更新成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"更新班级信息失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"更新班级信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"更新班级信息失败\");\n        return false;\n    }\n};\n/**\n * 删除班级\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const deleteClass = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n        if (response.data.code === 200) {\n            notification.success(\"班级删除成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"删除班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"删除班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"删除班级失败\");\n        return false;\n    }\n};\n/**\n * 转移班级\n * @param classId 班级ID\n * @param newTeacherId 新教师ID\n * @param transferType 转移类型\n * @returns Promise<boolean>\n */ const transferClass = async (classId, newTeacherId, transferType)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.transferClass(classId, newTeacherId, transferType);\n        if (response.data.code === 200) {\n            notification.success(\"班级转移成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"转移班级失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"转移班级失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转移班级失败\");\n        return false;\n    }\n};\n/**\n * 生成班级邀请码\n * @param classId 班级ID\n * @returns Promise<string | null>\n */ const generateClassInviteCode = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.generateInviteCode(classId);\n        if (response.data.code === 200) {\n            const inviteCode = response.data.data.inviteCode;\n            notification.success(\"邀请码生成成功\");\n            return inviteCode;\n        } else {\n            notification.error(response.data.message || \"生成邀请码失败\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"生成邀请码失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"生成邀请码失败\");\n        return null;\n    }\n};\n/**\n * 搜索教师\n * @param phone 教师手机号\n * @returns Promise<any | null>\n */ const searchTeacherByPhone = async (phone)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.searchTeacherByPhone(phone);\n        if (response.data.code === 200) {\n            return response.data.data;\n        } else {\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n            notification.error(response.data.message || \"未找到该教师\");\n            return null;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"搜索教师失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        return null;\n    }\n};\n/**\n * 导出学生信息\n * @param students 学生列表\n * @param className 班级名称\n */ const exportStudentsData = (students, className)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        if (students.length === 0) {\n            notification.warning(\"没有学生数据可以导出\");\n            return;\n        }\n        // 准备导出数据\n        const exportData = students.map((student, index)=>{\n            var _student_currentTemplate;\n            return {\n                序号: index + 1,\n                学号: student.studentNumber || \"\",\n                姓名: student.nickName || \"\",\n                总积分: student.totalPoints || 0,\n                可用积分: student.availablePoints || 0,\n                当前模板: ((_student_currentTemplate = student.currentTemplate) === null || _student_currentTemplate === void 0 ? void 0 : _student_currentTemplate.templateName) || \"无\"\n            };\n        });\n        // 转换为CSV格式\n        const headers = Object.keys(exportData[0]);\n        const csvContent = [\n            headers.join(\",\"),\n            ...exportData.map((row)=>headers.map((header)=>'\"'.concat(row[header], '\"')).join(\",\"))\n        ].join(\"\\n\");\n        // 创建下载链接\n        const blob = new Blob([\n            \"\\uFEFF\" + csvContent\n        ], {\n            type: \"text/csv;charset=utf-8;\"\n        });\n        const link = document.createElement(\"a\");\n        const url = URL.createObjectURL(blob);\n        link.setAttribute(\"href\", url);\n        link.setAttribute(\"download\", \"\".concat(className, \"_学生信息_\").concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        link.style.visibility = \"hidden\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        notification.success(\"学生信息导出成功\");\n    } catch (error) {\n        console.error(\"导出学生信息失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(\"导出学生信息失败\");\n    }\n};\n/**\n * 使用API导出学生信息\n * @param classId 班级ID\n * @returns Promise<boolean>\n */ const exportStudentsViaAPI = async (classId)=>{\n    try {\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        const hideLoading = notification.loading(\"正在导出学生信息...\");\n        const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_2__.studentApi.exportStudents(classId);\n        if (hideLoading) {\n            hideLoading.close();\n        }\n        if (response.data.code === 200) {\n            notification.success(\"导出学生成功\");\n            return true;\n        } else {\n            notification.error(response.data.message || \"导出学生失败\");\n            return false;\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"导出学生失败:\", error);\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"导出学生失败，请稍后重试\");\n        return false;\n    }\n};\n/**\n * 验证班级名称\n * @param className 班级名称\n * @returns { isValid: boolean; message?: string }\n */ const validateClassName = (className)=>{\n    if (!className || className.trim().length === 0) {\n        return {\n            isValid: false,\n            message: \"班级名称不能为空\"\n        };\n    }\n    if (className.length > 50) {\n        return {\n            isValid: false,\n            message: \"班级名称不能超过50个字符\"\n        };\n    }\n    // 检查特殊字符\n    const invalidChars = /[<>:\"/\\\\|?*]/;\n    if (invalidChars.test(className)) {\n        return {\n            isValid: false,\n            message: '班级名称不能包含特殊字符 < > : \" / \\\\ | ? *'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * 编辑班级信息（增强版）\n * @param classId 班级ID\n * @param values 班级信息\n * @returns Promise<{ success: boolean; data?: any }>\n */ const editClassInfo = async (classId, values)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        console.log(\"开始编辑班级:\", {\n            classId: classId,\n            values: values\n        });\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            className: values.className,\n            grade: values.grade || \"\"\n        });\n        console.log(\"编辑班级API响应:\", response);\n        if (response.data.code === 200) {\n            console.log(\"编辑班级成功\");\n            notification.success(\"编辑班级成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            console.error(\"编辑班级失败 - API返回错误:\", {\n                code: response.data.code,\n                message: response.data.message,\n                data: response.data\n            });\n            notification.error(response.data.message || \"编辑班级失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response, _error_response1, _error_response_data, _error_response2;\n        console.error(\"编辑班级失败 - 请求异常:\", {\n            error: error,\n            message: error.message,\n            response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n            status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n        });\n        notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 移出协助教师\n * @param classId 班级ID\n * @returns Promise<{ success: boolean; data?: any }>\n */ const removeAssistantTeacher = async (classId)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.updateClass(classId, {\n            assistantTeacherId: 0\n        });\n        if (response.data.code === 200) {\n            notification.success(\"移出协助教师成功\");\n            return {\n                success: true,\n                data: response.data.data\n            };\n        } else {\n            notification.error(response.data.message || \"移出协助教师失败\");\n            return {\n                success: false\n            };\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        console.error(\"移出协助教师失败:\", error);\n        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        return {\n            success: false\n        };\n    }\n};\n/**\n * 删除班级（增强版，包含学生检查）\n * @param classId 班级ID\n * @param students 学生列表\n * @param onSuccess 成功回调\n * @returns Promise<boolean>\n */ const deleteClassWithCheck = async (classId, students, onSuccess)=>{\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_0__.GetNotification)();\n    // 先检查是否有学生\n    if (students.length > 0) {\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning({\n            title: \"无法删除班级\",\n            content: \"该班级还有 \".concat(students.length, \" 名学生，请先移出所有学生后再删除班级。\"),\n            okText: \"确定\"\n        });\n        return false;\n    }\n    return new Promise((resolve)=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].confirm({\n            title: \"确认删除班级\",\n            content: \"删除后无法恢复，确定要删除这个班级吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.deleteClass(classId);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n                        resolve(true);\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                        resolve(false);\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                    resolve(false);\n                }\n            },\n            onCancel: ()=>{\n                resolve(false);\n            }\n        });\n    });\n};\n// 学校数据缓存\nlet schoolsCache = null;\nconst SCHOOL_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存\n/**\n * 获取用户关联的学校列表（支持缓存）\n * @param useCache 是否使用缓存，默认true\n * @returns Promise<School[]>\n */ const fetchUserSchools = async function() {\n    let useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    try {\n        // 检查缓存\n        if (useCache && schoolsCache && Date.now() - schoolsCache.timestamp < SCHOOL_CACHE_DURATION) {\n            console.log(\"使用学校列表缓存\");\n            return schoolsCache.data;\n        }\n        console.log(\"=== 开始获取用户学校列表 ===\");\n        // 从localStorage获取用户信息\n        const userInfo = localStorage.getItem(\"user\");\n        if (!userInfo) {\n            console.error(\"未找到用户信息，请检查登录状态\");\n            throw new Error(\"用户未登录\");\n        }\n        const user = JSON.parse(userInfo);\n        console.log(\"解析的用户信息:\", user);\n        // 根据项目中其他组件的使用方式，尝试多种可能的用户ID字段名\n        const userId = user.id || user.userId || user.teacherId;\n        if (!userId) {\n            console.error(\"用户信息中未找到ID字段:\", user);\n            throw new Error(\"用户ID不存在\");\n        }\n        console.log(\"使用的用户ID:\", userId);\n        // 使用项目中已有的API函数\n        const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_3__.schoolApi.getUserSchools();\n        console.log(\"获取学校数据:\", response);\n        if (response.data.code === 200 && response.data.data) {\n            // 确保数据是数组\n            const schoolsData = Array.isArray(response.data.data) ? response.data.data : [];\n            console.log(\"成功获取学校列表:\", schoolsData);\n            // 更新缓存\n            schoolsCache = {\n                data: schoolsData,\n                timestamp: Date.now()\n            };\n            return schoolsData;\n        } else {\n            console.error(\"API返回错误:\", response.data);\n            throw new Error(\"获取学校列表失败: \".concat(response.data.msg || \"未知错误\"));\n        }\n    } catch (error) {\n        console.error(\"获取学校列表失败:\", error);\n        throw error;\n    }\n};\n/**\n * 清除学校列表缓存\n */ const clearSchoolsCache = ()=>{\n    schoolsCache = null;\n    console.log(\"已清除学校列表缓存\");\n};\n/**\n * 获取学校缓存信息\n * @returns 缓存信息或null\n */ const getSchoolsCacheInfo = ()=>{\n    if (!schoolsCache) return null;\n    const isExpired = Date.now() - schoolsCache.timestamp >= SCHOOL_CACHE_DURATION;\n    return {\n        dataCount: schoolsCache.data.length,\n        timestamp: schoolsCache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : SCHOOL_CACHE_DURATION - (Date.now() - schoolsCache.timestamp)\n    };\n};\n/**\n * 预加载学校数据\n * @param delay 延迟时间（毫秒），默认1000ms\n */ const preloadSchoolsData = function() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1000;\n    setTimeout(async ()=>{\n        try {\n            await fetchUserSchools();\n            console.log(\"学校数据预加载完成\");\n        } catch (error) {\n            console.error(\"学校数据预加载失败:\", error);\n        }\n    }, delay);\n};\n// 缓存持续时间：5分钟\nconst TEACHER_CACHE_DURATION = 5 * 60 * 1000;\n// 全局缓存对象\nlet teacherClassesCache = {};\n// 已移除：fetchTeacherSchools - 请使用 classService.getTeacherSchools()\n// 已移除：fetchTeacherClasses - 请使用 classService.getSchoolClasses()\n// 已移除：updateTeacherClassCache - 请使用 classService 的缓存系统\n// 已移除：clearTeacherClassCache - 请使用 classService 的缓存系统\n/**\n * 调试函数：获取当前缓存状态\n */ const debugClassCache = ()=>{\n    console.log(\"=== 班级缓存调试信息 ===\");\n    console.log(\"缓存持续时间:\", TEACHER_CACHE_DURATION, \"ms\");\n    console.log(\"当前时间:\", new Date().toISOString());\n    console.log(\"缓存内容:\");\n    Object.keys(teacherClassesCache).forEach((schoolId)=>{\n        const cache = teacherClassesCache[Number(schoolId)];\n        const isExpired = Date.now() - cache.timestamp > TEACHER_CACHE_DURATION;\n        console.log(\"  学校\".concat(schoolId, \":\"), {\n            班级数量: cache.data.length,\n            缓存时间: new Date(cache.timestamp).toISOString(),\n            是否过期: isExpired,\n            剩余时间: isExpired ? \"已过期\" : \"\".concat(Math.round((TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)) / 1000), \"秒\")\n        });\n    });\n    console.log(\"=== 调试信息结束 ===\");\n};\n/**\n * 获取教师班级缓存状态\n * @param schoolId 学校ID\n * @returns 缓存信息或null\n */ const getTeacherClassCacheInfo = (schoolId)=>{\n    const cache = teacherClassesCache[schoolId];\n    if (!cache) return null;\n    const isExpired = Date.now() - cache.timestamp >= TEACHER_CACHE_DURATION;\n    return {\n        schoolId: cache.schoolId,\n        dataCount: cache.data.length,\n        timestamp: cache.timestamp,\n        isExpired,\n        remainingTime: isExpired ? 0 : TEACHER_CACHE_DURATION - (Date.now() - cache.timestamp)\n    };\n};\n/**\n * 处理学校选择的业务逻辑\n * @param school 选中的学校\n * @param teacherId 教师ID\n * @param callbacks 回调函数集合\n */ const handleSchoolSelection = async (school, teacherId, callbacks)=>{\n    try {\n        var // 通知学校选择变化\n        _callbacks_onSchoolSelect, // 切换到班级管理页面\n        _callbacks_onMenuItemClick, // 通知开始加载班级数据\n        _callbacks_onClassesUpdate, // 通知班级数据更新完成\n        _callbacks_onClassesUpdate1;\n        console.log(\"处理学校选择:\", school.schoolName);\n        (_callbacks_onSchoolSelect = callbacks.onSchoolSelect) === null || _callbacks_onSchoolSelect === void 0 ? void 0 : _callbacks_onSchoolSelect.call(callbacks, school);\n        (_callbacks_onMenuItemClick = callbacks.onMenuItemClick) === null || _callbacks_onMenuItemClick === void 0 ? void 0 : _callbacks_onMenuItemClick.call(callbacks, \"班级管理\");\n        (_callbacks_onClassesUpdate = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate === void 0 ? void 0 : _callbacks_onClassesUpdate.call(callbacks, [], true, null);\n        // 获取班级列表\n        const classList = await fetchTeacherClasses(school.id, teacherId);\n        (_callbacks_onClassesUpdate1 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate1 === void 0 ? void 0 : _callbacks_onClassesUpdate1.call(callbacks, classList, false, null);\n        console.log(\"学校选择处理完成，班级数量:\", classList.length);\n    } catch (error) {\n        var _callbacks_onClassesUpdate2;\n        console.error(\"处理学校选择失败:\", error);\n        (_callbacks_onClassesUpdate2 = callbacks.onClassesUpdate) === null || _callbacks_onClassesUpdate2 === void 0 ? void 0 : _callbacks_onClassesUpdate2.call(callbacks, [], false, error.message || \"获取班级列表失败\");\n    }\n};\n/**\n * 初始化学校选择模态框状态\n * @returns SchoolModalState\n */ const initSchoolModalState = ()=>({\n        schools: [],\n        loading: false,\n        error: null,\n        mounted: false\n    });\n/**\n * 处理学校选择模态框的数据加载\n * @param userId 用户ID\n * @param setState 状态更新函数\n * @param useCache 是否使用缓存\n */ const handleSchoolModalDataLoad = async function(userId, setState) {\n    let useCache = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    if (!userId) {\n        setState({\n            error: \"用户未登录\"\n        });\n        return;\n    }\n    try {\n        setState({\n            loading: true,\n            error: null\n        });\n        const schoolsData = await fetchUserSchools(useCache);\n        setState({\n            schools: schoolsData,\n            loading: false\n        });\n    } catch (err) {\n        console.error(\"获取学校列表失败:\", err);\n        setState({\n            error: \"获取学校列表失败，请稍后重试\",\n            loading: false\n        });\n    }\n};\n/**\n * 处理学校选择模态框的重试逻辑\n * @param setState 状态更新函数\n * @param userId 用户ID\n */ const handleSchoolModalRetry = async (setState, userId)=>{\n    await handleSchoolModalDataLoad(userId, setState, false);\n};\n/**\n * 获取班级学生列表（带通知）\n * @param classId 班级ID\n * @param notification 通知组件\n * @returns Promise<Student[]>\n */ const fetchClassStudentsWithNotification = async (classId, notification)=>{\n    try {\n        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_1__.classApi.getClassStudents(classId);\n        if (response.data.code === 200) {\n            return response.data.data || [];\n        } else {\n            console.error(\"获取学生列表失败:\", response.data.message);\n            notification.error(\"获取学生列表失败\");\n            return [];\n        }\n    } catch (error) {\n        console.error(\"获取学生列表失败:\", error);\n        notification.error(\"获取学生列表失败\");\n        return [];\n    }\n};\n/**\n * 获取学生能量信息（优化版本）\n * @param userIds 用户ID数组\n * @param notification 通知组件\n * @returns Promise<Map<number, number>>\n */ const fetchStudentPoints = async (userIds, notification)=>{\n    if (userIds.length === 0) {\n        return new Map();\n    }\n    try {\n        const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n        console.log(\"开始获取学生能量信息，userIds:\", userIds);\n        console.log(\"使用API端点:\", \"\".concat(pointsApi.basePointUrl, \"/batch-total\"));\n        // 设置3秒超时，避免长时间等待影响用户体验\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"请求超时\")), 3000);\n        });\n        const apiPromise = pointsApi.getBatchStudentPoints(userIds);\n        const response = await Promise.race([\n            apiPromise,\n            timeoutPromise\n        ]);\n        const pointsMap = new Map();\n        console.log(\"批量积分响应\", response);\n        if (response.data.code === 200) {\n            const data = response.data.data;\n            // 遍历返回的学生积分数据\n            for(const userId in data){\n                const studentData = data[userId];\n                if (studentData) {\n                    const totalPoints = studentData.totalPoints || 0;\n                    const availablePoints = studentData.availablePoints || 0;\n                    // 修正计算逻辑：与 assign-points-modal.tsx 保持一致\n                    // totalPoints: 总积分, availablePoints: 已使用积分\n                    // 可分配积分 = 总积分 - 已使用积分\n                    const remainingPoints = totalPoints - availablePoints;\n                    pointsMap.set(Number(userId), Math.max(0, remainingPoints));\n                } else {\n                    pointsMap.set(Number(userId), 0);\n                }\n            }\n            // 确保所有请求的用户都有数据\n            userIds.forEach((uid)=>{\n                if (!pointsMap.has(uid)) {\n                    pointsMap.set(uid, 0);\n                }\n            });\n            return pointsMap;\n        } else {\n            // API返回错误，设置默认值但不显示错误提示（避免干扰用户体验）\n            userIds.forEach((uid)=>pointsMap.set(uid, 0));\n            console.warn(\"获取学生能量信息失败，使用默认值:\", response.data.message);\n            return pointsMap;\n        }\n    } catch (error) {\n        var _error_response, _error_response1;\n        console.error(\"批量获取学生能量失败:\", error);\n        // 设置默认值，让用户可以继续操作\n        const pointsMap = new Map();\n        userIds.forEach((uid)=>pointsMap.set(uid, 0));\n        // 根据错误类型决定是否显示提示\n        if (error.message === \"请求超时\") {\n            console.warn(\"获取能量信息超时，使用默认值，用户可继续操作\");\n        // 不显示错误提示，避免干扰用户体验\n        } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n            console.warn(\"服务器错误，使用默认能量值\");\n        // 服务器错误时也不显示提示，让用户可以继续操作\n        } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n            // 权限问题可能需要用户知道\n            notification.warning(\"暂无法获取能量信息，已设置默认值\");\n        }\n        // 其他错误也不显示提示，保持良好的用户体验\n        return pointsMap;\n    }\n};\n/**\n * 计算所有学生的最低可分配能量\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 最低可分配能量\n */ const getMinAvailablePoints = (selectedStudents, studentPointsMap)=>{\n    if (selectedStudents.length === 0) return 0;\n    const selectedStudentPoints = selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n    return Math.min(...selectedStudentPoints);\n};\n/**\n * 获取当前分配方式的提示信息\n * @param selectedDistribution 选中的分配方式\n * @param selectedStudents 选中的学生ID数组\n * @param studentPointsMap 学生能量映射\n * @returns 提示信息对象\n */ const getEnergyDisplayInfo = (selectedDistribution, selectedStudents, studentPointsMap)=>{\n    if (selectedDistribution === \"assign\" || selectedDistribution === \"distribute\") {\n        return {\n            label: \"最低可分配能量\",\n            value: getMinAvailablePoints(selectedStudents, studentPointsMap)\n        };\n    }\n    return {\n        label: \"\",\n        value: 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvY2xhc3NVdGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEU7QUFDL0M7QUFDcUI7QUFDSTtBQUNGO0FBNkJwRDs7Q0FFQyxHQUVEOzs7OztDQUtDLEdBQ00sTUFBTUssa0JBQWtCLE9BQzdCQyxXQUNBQztJQUVBLElBQUk7UUFDRixNQUFNQyxlQUFlUiwwRkFBZUE7UUFFcEMsTUFBTVMsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ1EsaUJBQWlCLENBQUM7WUFDaERDLElBQUlMLFVBQVVLLEVBQUU7WUFDaEJDLFdBQVdMLE9BQU9LLFNBQVM7UUFDN0I7UUFFQSxJQUFJSCxTQUFTSSxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO1lBQzlCTixhQUFhTyxPQUFPLENBQUM7WUFDckIsT0FBTztRQUNULE9BQU87WUFDTFAsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTtZQUM1QyxPQUFPO1FBQ1Q7SUFDRixFQUFFLE9BQU9ELE9BQVk7WUFHQUEsc0JBQUFBO1FBRm5CRSxRQUFRRixLQUFLLENBQUMsYUFBYUE7UUFDM0IsTUFBTVIsZUFBZVIsMEZBQWVBO1FBQ3BDUSxhQUFhUSxLQUFLLENBQUNBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSx1QkFBQUEsZ0JBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCQyxPQUFPLEtBQUk7UUFDcEQsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGOzs7O0NBSUMsR0FDTSxNQUFNRSxjQUFjLE9BQU9DO0lBQ2hDLElBQUk7UUFDRixNQUFNWixlQUFlUiwwRkFBZUE7UUFFcEMsTUFBTVMsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ2lCLFdBQVcsQ0FBQ0M7UUFFNUMsSUFBSVgsU0FBU0ksSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztZQUM5Qk4sYUFBYU8sT0FBTyxDQUFDO1lBQ3JCLE9BQU87UUFDVCxPQUFPO1lBQ0xQLGFBQWFRLEtBQUssQ0FBQ1AsU0FBU0ksSUFBSSxDQUFDSSxPQUFPLElBQUk7WUFDNUMsT0FBTztRQUNUO0lBQ0YsRUFBRSxPQUFPRCxPQUFZO1lBR0FBLHNCQUFBQTtRQUZuQkUsUUFBUUYsS0FBSyxDQUFDLFdBQVdBO1FBQ3pCLE1BQU1SLGVBQWVSLDBGQUFlQTtRQUNwQ1EsYUFBYVEsS0FBSyxDQUFDQSxFQUFBQSxrQkFBQUEsTUFBTVAsUUFBUSxjQUFkTyx1Q0FBQUEsdUJBQUFBLGdCQUFnQkgsSUFBSSxjQUFwQkcsMkNBQUFBLHFCQUFzQkMsT0FBTyxLQUFJO1FBQ3BELE9BQU87SUFDVDtBQUNGLEVBQUU7QUFFRjs7Ozs7O0NBTUMsR0FDTSxNQUFNSSxnQkFBZ0IsT0FDM0JELFNBQ0FFLGNBQ0FDO0lBRUEsSUFBSTtRQUNGLE1BQU1mLGVBQWVSLDBGQUFlQTtRQUVwQyxNQUFNUyxXQUFXLE1BQU1QLG9EQUFRQSxDQUFDbUIsYUFBYSxDQUFDRCxTQUFTRSxjQUFjQztRQUVyRSxJQUFJZCxTQUFTSSxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO1lBQzlCTixhQUFhTyxPQUFPLENBQUM7WUFDckIsT0FBTztRQUNULE9BQU87WUFDTFAsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTtZQUM1QyxPQUFPO1FBQ1Q7SUFDRixFQUFFLE9BQU9ELE9BQVk7WUFHQUEsc0JBQUFBO1FBRm5CRSxRQUFRRixLQUFLLENBQUMsV0FBV0E7UUFDekIsTUFBTVIsZUFBZVIsMEZBQWVBO1FBQ3BDUSxhQUFhUSxLQUFLLENBQUNBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSx1QkFBQUEsZ0JBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCQyxPQUFPLEtBQUk7UUFDcEQsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGOzs7O0NBSUMsR0FDTSxNQUFNTywwQkFBMEIsT0FBT0o7SUFDNUMsSUFBSTtRQUNGLE1BQU1aLGVBQWVSLDBGQUFlQTtRQUVwQyxNQUFNUyxXQUFXLE1BQU1QLG9EQUFRQSxDQUFDdUIsa0JBQWtCLENBQUNMO1FBRW5ELElBQUlYLFNBQVNJLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7WUFDOUIsTUFBTVksYUFBYWpCLFNBQVNJLElBQUksQ0FBQ0EsSUFBSSxDQUFDYSxVQUFVO1lBQ2hEbEIsYUFBYU8sT0FBTyxDQUFDO1lBQ3JCLE9BQU9XO1FBQ1QsT0FBTztZQUNMbEIsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTtZQUM1QyxPQUFPO1FBQ1Q7SUFDRixFQUFFLE9BQU9ELE9BQVk7WUFHQUEsc0JBQUFBO1FBRm5CRSxRQUFRRixLQUFLLENBQUMsWUFBWUE7UUFDMUIsTUFBTVIsZUFBZVIsMEZBQWVBO1FBQ3BDUSxhQUFhUSxLQUFLLENBQUNBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSx1QkFBQUEsZ0JBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCQyxPQUFPLEtBQUk7UUFDcEQsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGOzs7O0NBSUMsR0FDTSxNQUFNVSx1QkFBdUIsT0FBT0M7SUFDekMsSUFBSTtRQUNGLE1BQU1uQixXQUFXLE1BQU1QLG9EQUFRQSxDQUFDeUIsb0JBQW9CLENBQUNDO1FBRXJELElBQUluQixTQUFTSSxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO1lBQzlCLE9BQU9MLFNBQVNJLElBQUksQ0FBQ0EsSUFBSTtRQUMzQixPQUFPO1lBQ0wsTUFBTUwsZUFBZVIsMEZBQWVBO1lBQ3BDUSxhQUFhUSxLQUFLLENBQUNQLFNBQVNJLElBQUksQ0FBQ0ksT0FBTyxJQUFJO1lBQzVDLE9BQU87UUFDVDtJQUNGLEVBQUUsT0FBT0QsT0FBWTtZQUdBQSxzQkFBQUE7UUFGbkJFLFFBQVFGLEtBQUssQ0FBQyxXQUFXQTtRQUN6QixNQUFNUixlQUFlUiwwRkFBZUE7UUFDcENRLGFBQWFRLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sdUNBQUFBLHVCQUFBQSxnQkFBZ0JILElBQUksY0FBcEJHLDJDQUFBQSxxQkFBc0JDLE9BQU8sS0FBSTtRQUNwRCxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUY7Ozs7Q0FJQyxHQUNNLE1BQU1ZLHFCQUFxQixDQUFDQyxVQUFpQmxCO0lBQ2xELElBQUk7UUFDRixNQUFNSixlQUFlUiwwRkFBZUE7UUFFcEMsSUFBSThCLFNBQVNDLE1BQU0sS0FBSyxHQUFHO1lBQ3pCdkIsYUFBYXdCLE9BQU8sQ0FBQztZQUNyQjtRQUNGO1FBRUEsU0FBUztRQUNULE1BQU1DLGFBQWFILFNBQVNJLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQztnQkFNbENEO21CQU42QztnQkFDbkRFLElBQUlELFFBQVE7Z0JBQ1pFLElBQUlILFFBQVFJLGFBQWEsSUFBSTtnQkFDN0JDLElBQUlMLFFBQVFNLFFBQVEsSUFBSTtnQkFDeEJDLEtBQUtQLFFBQVFRLFdBQVcsSUFBSTtnQkFDNUJDLE1BQU1ULFFBQVFVLGVBQWUsSUFBSTtnQkFDakNDLE1BQU1YLEVBQUFBLDJCQUFBQSxRQUFRWSxlQUFlLGNBQXZCWiwrQ0FBQUEseUJBQXlCYSxZQUFZLEtBQUk7WUFDakQ7O1FBRUEsV0FBVztRQUNYLE1BQU1DLFVBQVVDLE9BQU9DLElBQUksQ0FBQ2xCLFVBQVUsQ0FBQyxFQUFFO1FBQ3pDLE1BQU1tQixhQUFhO1lBQ2pCSCxRQUFRSSxJQUFJLENBQUM7ZUFDVnBCLFdBQVdDLEdBQUcsQ0FBQ29CLENBQUFBLE1BQU9MLFFBQVFmLEdBQUcsQ0FBQ3FCLENBQUFBLFNBQVUsSUFBb0MsT0FBaENELEdBQUcsQ0FBQ0MsT0FBMkIsRUFBQyxNQUFJRixJQUFJLENBQUM7U0FDN0YsQ0FBQ0EsSUFBSSxDQUFDO1FBRVAsU0FBUztRQUNULE1BQU1HLE9BQU8sSUFBSUMsS0FBSztZQUFDLFdBQVdMO1NBQVcsRUFBRTtZQUFFTSxNQUFNO1FBQTBCO1FBQ2pGLE1BQU1DLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztRQUNwQyxNQUFNQyxNQUFNQyxJQUFJQyxlQUFlLENBQUNSO1FBRWhDRyxLQUFLTSxZQUFZLENBQUMsUUFBUUg7UUFDMUJILEtBQUtNLFlBQVksQ0FBQyxZQUFZLEdBQXFCLE9BQWxCckQsV0FBVSxVQUErQyxPQUF2QyxJQUFJc0QsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBQztRQUMxRlQsS0FBS1UsS0FBSyxDQUFDQyxVQUFVLEdBQUc7UUFFeEJWLFNBQVNXLElBQUksQ0FBQ0MsV0FBVyxDQUFDYjtRQUMxQkEsS0FBS2MsS0FBSztRQUNWYixTQUFTVyxJQUFJLENBQUNHLFdBQVcsQ0FBQ2Y7UUFFMUJuRCxhQUFhTyxPQUFPLENBQUM7SUFDdkIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyxhQUFhQTtRQUMzQixNQUFNUixlQUFlUiwwRkFBZUE7UUFDcENRLGFBQWFRLEtBQUssQ0FBQztJQUNyQjtBQUNGLEVBQUU7QUFFRjs7OztDQUlDLEdBQ00sTUFBTTJELHVCQUF1QixPQUFPdkQ7SUFDekMsSUFBSTtRQUNGLE1BQU1aLGVBQWVSLDBGQUFlQTtRQUNwQyxNQUFNNEUsY0FBY3BFLGFBQWFxRSxPQUFPLENBQUM7UUFFekMsTUFBTXBFLFdBQVcsTUFBTU4sd0RBQVVBLENBQUMyRSxjQUFjLENBQUMxRDtRQUVqRCxJQUFJd0QsYUFBYTtZQUNmQSxZQUFZRyxLQUFLO1FBQ25CO1FBRUEsSUFBSXRFLFNBQVNJLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7WUFDOUJOLGFBQWFPLE9BQU8sQ0FBQztZQUNyQixPQUFPO1FBQ1QsT0FBTztZQUNMUCxhQUFhUSxLQUFLLENBQUNQLFNBQVNJLElBQUksQ0FBQ0ksT0FBTyxJQUFJO1lBQzVDLE9BQU87UUFDVDtJQUNGLEVBQUUsT0FBT0QsT0FBWTtZQUdBQSxzQkFBQUE7UUFGbkJFLFFBQVFGLEtBQUssQ0FBQyxXQUFXQTtRQUN6QixNQUFNUixlQUFlUiwwRkFBZUE7UUFDcENRLGFBQWFRLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sdUNBQUFBLHVCQUFBQSxnQkFBZ0JILElBQUksY0FBcEJHLDJDQUFBQSxxQkFBc0JDLE9BQU8sS0FBSTtRQUNwRCxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUY7Ozs7Q0FJQyxHQUNNLE1BQU0rRCxvQkFBb0IsQ0FBQ3BFO0lBQ2hDLElBQUksQ0FBQ0EsYUFBYUEsVUFBVXFFLElBQUksR0FBR2xELE1BQU0sS0FBSyxHQUFHO1FBQy9DLE9BQU87WUFBRW1ELFNBQVM7WUFBT2pFLFNBQVM7UUFBVztJQUMvQztJQUVBLElBQUlMLFVBQVVtQixNQUFNLEdBQUcsSUFBSTtRQUN6QixPQUFPO1lBQUVtRCxTQUFTO1lBQU9qRSxTQUFTO1FBQWdCO0lBQ3BEO0lBRUEsU0FBUztJQUNULE1BQU1rRSxlQUFlO0lBQ3JCLElBQUlBLGFBQWFDLElBQUksQ0FBQ3hFLFlBQVk7UUFDaEMsT0FBTztZQUFFc0UsU0FBUztZQUFPakUsU0FBUztRQUFrQztJQUN0RTtJQUVBLE9BQU87UUFBRWlFLFNBQVM7SUFBSztBQUN6QixFQUFFO0FBRUY7Ozs7O0NBS0MsR0FDTSxNQUFNRyxnQkFBZ0IsT0FDM0JqRSxTQUNBYjtJQUVBLE1BQU1DLGVBQWVSLDBGQUFlQTtJQUVwQyxJQUFJO1FBQ0ZrQixRQUFRb0UsR0FBRyxDQUFDLFdBQVc7WUFDckJsRSxTQUFTQTtZQUNUYixRQUFRQTtRQUNWO1FBRUEsTUFBTUUsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ3FGLFdBQVcsQ0FBQ25FLFNBQVM7WUFDbkRSLFdBQVdMLE9BQU9LLFNBQVM7WUFDM0I0RSxPQUFPakYsT0FBT2lGLEtBQUssSUFBSTtRQUN6QjtRQUVBdEUsUUFBUW9FLEdBQUcsQ0FBQyxjQUFjN0U7UUFFMUIsSUFBSUEsU0FBU0ksSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztZQUM5QkksUUFBUW9FLEdBQUcsQ0FBQztZQUNaOUUsYUFBYU8sT0FBTyxDQUFDO1lBQ3JCLE9BQU87Z0JBQUVBLFNBQVM7Z0JBQU1GLE1BQU1KLFNBQVNJLElBQUksQ0FBQ0EsSUFBSTtZQUFDO1FBQ25ELE9BQU87WUFDTEssUUFBUUYsS0FBSyxDQUFDLHFCQUFxQjtnQkFDakNGLE1BQU1MLFNBQVNJLElBQUksQ0FBQ0MsSUFBSTtnQkFDeEJHLFNBQVNSLFNBQVNJLElBQUksQ0FBQ0ksT0FBTztnQkFDOUJKLE1BQU1KLFNBQVNJLElBQUk7WUFDckI7WUFDQUwsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTtZQUM1QyxPQUFPO2dCQUFFRixTQUFTO1lBQU07UUFDMUI7SUFDRixFQUFFLE9BQU9DLE9BQVk7WUFJUEEsaUJBQ0ZBLGtCQUdTQSxzQkFBQUE7UUFQbkJFLFFBQVFGLEtBQUssQ0FBQyxrQkFBa0I7WUFDOUJBLE9BQU9BO1lBQ1BDLFNBQVNELE1BQU1DLE9BQU87WUFDdEJSLFFBQVEsR0FBRU8sa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sc0NBQUFBLGdCQUFnQkgsSUFBSTtZQUM5QjRFLE1BQU0sR0FBRXpFLG1CQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSxpQkFBZ0J5RSxNQUFNO1FBQ2hDO1FBRUFqRixhQUFhUSxLQUFLLENBQUNBLEVBQUFBLG1CQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHdDQUFBQSx1QkFBQUEsaUJBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCQyxPQUFPLEtBQUk7UUFDcEQsT0FBTztZQUFFRixTQUFTO1FBQU07SUFDMUI7QUFDRixFQUFFO0FBRUY7Ozs7Q0FJQyxHQUNNLE1BQU0yRSx5QkFBeUIsT0FBT3RFO0lBQzNDLE1BQU1aLGVBQWVSLDBGQUFlQTtJQUVwQyxJQUFJO1FBQ0YsTUFBTVMsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ3FGLFdBQVcsQ0FBQ25FLFNBQVM7WUFDbkR1RSxvQkFBb0I7UUFDdEI7UUFFQSxJQUFJbEYsU0FBU0ksSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztZQUM5Qk4sYUFBYU8sT0FBTyxDQUFDO1lBQ3JCLE9BQU87Z0JBQUVBLFNBQVM7Z0JBQU1GLE1BQU1KLFNBQVNJLElBQUksQ0FBQ0EsSUFBSTtZQUFDO1FBQ25ELE9BQU87WUFDTEwsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTtZQUM1QyxPQUFPO2dCQUFFRixTQUFTO1lBQU07UUFDMUI7SUFDRixFQUFFLE9BQU9DLE9BQVk7WUFFQUEsc0JBQUFBO1FBRG5CRSxRQUFRRixLQUFLLENBQUMsYUFBYUE7UUFDM0JSLGFBQWFRLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sdUNBQUFBLHVCQUFBQSxnQkFBZ0JILElBQUksY0FBcEJHLDJDQUFBQSxxQkFBc0JDLE9BQU8sS0FBSTtRQUNwRCxPQUFPO1lBQUVGLFNBQVM7UUFBTTtJQUMxQjtBQUNGLEVBQUU7QUFFRjs7Ozs7O0NBTUMsR0FDTSxNQUFNNkUsdUJBQXVCLE9BQ2xDeEUsU0FDQVUsVUFDQStEO0lBRUEsTUFBTXJGLGVBQWVSLDBGQUFlQTtJQUVwQyxXQUFXO0lBQ1gsSUFBSThCLFNBQVNDLE1BQU0sR0FBRyxHQUFHO1FBQ3ZCOUIseUVBQUtBLENBQUMrQixPQUFPLENBQUM7WUFDWjhELE9BQU87WUFDUEMsU0FBUyxTQUF5QixPQUFoQmpFLFNBQVNDLE1BQU0sRUFBQztZQUNsQ2lFLFFBQVE7UUFDVjtRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU8sSUFBSUMsUUFBUSxDQUFDQztRQUNsQmpHLHlFQUFLQSxDQUFDa0csT0FBTyxDQUFDO1lBQ1pMLE9BQU87WUFDUEMsU0FBUztZQUNUQyxRQUFRO1lBQ1JJLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxVQUFVO1lBQ1ZDLE1BQU07Z0JBQ0osSUFBSTtvQkFDRixNQUFNOUYsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ2lCLFdBQVcsQ0FBQ0M7b0JBRTVDLElBQUlYLFNBQVNJLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7d0JBQzlCTixhQUFhTyxPQUFPLENBQUM7d0JBQ3JCOEUsc0JBQUFBLGdDQUFBQTt3QkFDQUssUUFBUTtvQkFDVixPQUFPO3dCQUNMMUYsYUFBYVEsS0FBSyxDQUFDUCxTQUFTSSxJQUFJLENBQUNJLE9BQU8sSUFBSTt3QkFDNUNpRixRQUFRO29CQUNWO2dCQUNGLEVBQUUsT0FBT2xGLE9BQVk7d0JBRWZBLHNCQUFBQTtvQkFESkUsUUFBUUYsS0FBSyxDQUFDLFdBQVdBO29CQUN6QixLQUFJQSxrQkFBQUEsTUFBTVAsUUFBUSxjQUFkTyx1Q0FBQUEsdUJBQUFBLGdCQUFnQkgsSUFBSSxjQUFwQkcsMkNBQUFBLHFCQUFzQkMsT0FBTyxFQUFFO3dCQUNqQ1QsYUFBYVEsS0FBSyxDQUFDQSxNQUFNUCxRQUFRLENBQUNJLElBQUksQ0FBQ0ksT0FBTztvQkFDaEQsT0FBTzt3QkFDTFQsYUFBYVEsS0FBSyxDQUFDO29CQUNyQjtvQkFDQWtGLFFBQVE7Z0JBQ1Y7WUFDRjtZQUNBTSxVQUFVO2dCQUNSTixRQUFRO1lBQ1Y7UUFDRjtJQUNGO0FBQ0YsRUFBRTtBQVVGLFNBQVM7QUFDVCxJQUFJTyxlQUFtQztBQUN2QyxNQUFNQyx3QkFBd0IsSUFBSSxLQUFLLE1BQU0sUUFBUTtBQUVyRDs7OztDQUlDLEdBQ00sTUFBTUMsbUJBQW1CO1FBQU9DLDRFQUFvQjtJQUN6RCxJQUFJO1FBQ0YsT0FBTztRQUNQLElBQUlBLFlBQVlILGdCQUFnQnZDLEtBQUsyQyxHQUFHLEtBQUtKLGFBQWFLLFNBQVMsR0FBR0osdUJBQXVCO1lBQzNGeEYsUUFBUW9FLEdBQUcsQ0FBQztZQUNaLE9BQU9tQixhQUFhNUYsSUFBSTtRQUMxQjtRQUVBSyxRQUFRb0UsR0FBRyxDQUFDO1FBRVosc0JBQXNCO1FBQ3RCLE1BQU15QixXQUFXQyxhQUFhQyxPQUFPLENBQUM7UUFFdEMsSUFBSSxDQUFDRixVQUFVO1lBQ2I3RixRQUFRRixLQUFLLENBQUM7WUFDZCxNQUFNLElBQUlrRyxNQUFNO1FBQ2xCO1FBRUEsTUFBTUMsT0FBT0MsS0FBS0MsS0FBSyxDQUFDTjtRQUN4QjdGLFFBQVFvRSxHQUFHLENBQUMsWUFBWTZCO1FBRXhCLGdDQUFnQztRQUNoQyxNQUFNRyxTQUFTSCxLQUFLeEcsRUFBRSxJQUFJd0csS0FBS0csTUFBTSxJQUFJSCxLQUFLSSxTQUFTO1FBRXZELElBQUksQ0FBQ0QsUUFBUTtZQUNYcEcsUUFBUUYsS0FBSyxDQUFDLGlCQUFpQm1HO1lBQy9CLE1BQU0sSUFBSUQsTUFBTTtRQUNsQjtRQUVBaEcsUUFBUW9FLEdBQUcsQ0FBQyxZQUFZZ0M7UUFFeEIsZ0JBQWdCO1FBQ2hCLE1BQU03RyxXQUFXLE1BQU1MLHNEQUFTQSxDQUFDb0gsY0FBYztRQUMvQ3RHLFFBQVFvRSxHQUFHLENBQUMsV0FBVzdFO1FBRXZCLElBQUlBLFNBQVNJLElBQUksQ0FBQ0MsSUFBSSxLQUFLLE9BQU9MLFNBQVNJLElBQUksQ0FBQ0EsSUFBSSxFQUFFO1lBQ3BELFVBQVU7WUFDVixNQUFNNEcsY0FBY0MsTUFBTUMsT0FBTyxDQUFDbEgsU0FBU0ksSUFBSSxDQUFDQSxJQUFJLElBQUlKLFNBQVNJLElBQUksQ0FBQ0EsSUFBSSxHQUFHLEVBQUU7WUFDL0VLLFFBQVFvRSxHQUFHLENBQUMsYUFBYW1DO1lBRXpCLE9BQU87WUFDUGhCLGVBQWU7Z0JBQ2I1RixNQUFNNEc7Z0JBQ05YLFdBQVc1QyxLQUFLMkMsR0FBRztZQUNyQjtZQUVBLE9BQU9ZO1FBQ1QsT0FBTztZQUNMdkcsUUFBUUYsS0FBSyxDQUFDLFlBQVlQLFNBQVNJLElBQUk7WUFDdkMsTUFBTSxJQUFJcUcsTUFBTSxhQUF5QyxPQUE1QnpHLFNBQVNJLElBQUksQ0FBQytHLEdBQUcsSUFBSTtRQUNwRDtJQUNGLEVBQUUsT0FBTzVHLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLGFBQWFBO1FBQzNCLE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNNkcsb0JBQW9CO0lBQy9CcEIsZUFBZTtJQUNmdkYsUUFBUW9FLEdBQUcsQ0FBQztBQUNkLEVBQUU7QUFFRjs7O0NBR0MsR0FDTSxNQUFNd0Msc0JBQXNCO0lBQ2pDLElBQUksQ0FBQ3JCLGNBQWMsT0FBTztJQUUxQixNQUFNc0IsWUFBWTdELEtBQUsyQyxHQUFHLEtBQUtKLGFBQWFLLFNBQVMsSUFBSUo7SUFDekQsT0FBTztRQUNMc0IsV0FBV3ZCLGFBQWE1RixJQUFJLENBQUNrQixNQUFNO1FBQ25DK0UsV0FBV0wsYUFBYUssU0FBUztRQUNqQ2lCO1FBQ0FFLGVBQWVGLFlBQVksSUFBSXJCLHdCQUF5QnhDLENBQUFBLEtBQUsyQyxHQUFHLEtBQUtKLGFBQWFLLFNBQVM7SUFDN0Y7QUFDRixFQUFFO0FBRUY7OztDQUdDLEdBQ00sTUFBTW9CLHFCQUFxQjtRQUFDQyx5RUFBZ0I7SUFDakRDLFdBQVc7UUFDVCxJQUFJO1lBQ0YsTUFBTXpCO1lBQ056RixRQUFRb0UsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPdEUsT0FBTztZQUNkRSxRQUFRRixLQUFLLENBQUMsY0FBY0E7UUFDOUI7SUFDRixHQUFHbUg7QUFDTCxFQUFFO0FBYUYsYUFBYTtBQUNiLE1BQU1FLHlCQUF5QixJQUFJLEtBQUs7QUFFeEMsU0FBUztBQUNULElBQUlDLHNCQUEwRCxDQUFDO0FBRS9ELGlFQUFpRTtBQUVqRSxnRUFBZ0U7QUFFaEUsdURBQXVEO0FBRXZELHNEQUFzRDtBQUV0RDs7Q0FFQyxHQUNNLE1BQU1DLGtCQUFrQjtJQUM3QnJILFFBQVFvRSxHQUFHLENBQUM7SUFDWnBFLFFBQVFvRSxHQUFHLENBQUMsV0FBVytDLHdCQUF3QjtJQUMvQ25ILFFBQVFvRSxHQUFHLENBQUMsU0FBUyxJQUFJcEIsT0FBT0MsV0FBVztJQUMzQ2pELFFBQVFvRSxHQUFHLENBQUM7SUFFWnBDLE9BQU9DLElBQUksQ0FBQ21GLHFCQUFxQkUsT0FBTyxDQUFDQyxDQUFBQTtRQUN2QyxNQUFNQyxRQUFRSixtQkFBbUIsQ0FBQ0ssT0FBT0YsVUFBVTtRQUNuRCxNQUFNVixZQUFZN0QsS0FBSzJDLEdBQUcsS0FBSzZCLE1BQU01QixTQUFTLEdBQUd1QjtRQUNqRG5ILFFBQVFvRSxHQUFHLENBQUMsT0FBZ0IsT0FBVG1ELFVBQVMsTUFBSTtZQUM5QkcsTUFBTUYsTUFBTTdILElBQUksQ0FBQ2tCLE1BQU07WUFDdkI4RyxNQUFNLElBQUkzRSxLQUFLd0UsTUFBTTVCLFNBQVMsRUFBRTNDLFdBQVc7WUFDM0MyRSxNQUFNZjtZQUNOZ0IsTUFBTWhCLFlBQVksUUFBUSxHQUFnRixPQUE3RWlCLEtBQUtDLEtBQUssQ0FBQyxDQUFDWix5QkFBMEJuRSxDQUFBQSxLQUFLMkMsR0FBRyxLQUFLNkIsTUFBTTVCLFNBQVMsS0FBSyxPQUFNO1FBQzVHO0lBQ0Y7SUFDQTVGLFFBQVFvRSxHQUFHLENBQUM7QUFDZCxFQUFFO0FBRUY7Ozs7Q0FJQyxHQUNNLE1BQU00RCwyQkFBMkIsQ0FBQ1Q7SUFDdkMsTUFBTUMsUUFBUUosbUJBQW1CLENBQUNHLFNBQVM7SUFDM0MsSUFBSSxDQUFDQyxPQUFPLE9BQU87SUFFbkIsTUFBTVgsWUFBWTdELEtBQUsyQyxHQUFHLEtBQUs2QixNQUFNNUIsU0FBUyxJQUFJdUI7SUFDbEQsT0FBTztRQUNMSSxVQUFVQyxNQUFNRCxRQUFRO1FBQ3hCVCxXQUFXVSxNQUFNN0gsSUFBSSxDQUFDa0IsTUFBTTtRQUM1QitFLFdBQVc0QixNQUFNNUIsU0FBUztRQUMxQmlCO1FBQ0FFLGVBQWVGLFlBQVksSUFBSU0seUJBQTBCbkUsQ0FBQUEsS0FBSzJDLEdBQUcsS0FBSzZCLE1BQU01QixTQUFTO0lBQ3ZGO0FBQ0YsRUFBRTtBQUVGOzs7OztDQUtDLEdBQ00sTUFBTXFDLHdCQUF3QixPQUNuQ0MsUUFDQTdCLFdBQ0E4QjtJQU1BLElBQUk7WUFHRixXQUFXO1FBQ1hBLDJCQUVBLFlBQVk7UUFDWkEsNEJBRUEsYUFBYTtRQUNiQSw0QkFLQSxhQUFhO1FBQ2JBO1FBZkFuSSxRQUFRb0UsR0FBRyxDQUFDLFdBQVc4RCxPQUFPRSxVQUFVO1NBR3hDRCw0QkFBQUEsVUFBVUUsY0FBYyxjQUF4QkYsZ0RBQUFBLCtCQUFBQSxXQUEyQkQ7U0FHM0JDLDZCQUFBQSxVQUFVRyxlQUFlLGNBQXpCSCxpREFBQUEsZ0NBQUFBLFdBQTRCO1NBRzVCQSw2QkFBQUEsVUFBVUksZUFBZSxjQUF6QkosaURBQUFBLGdDQUFBQSxXQUE0QixFQUFFLEVBQUUsTUFBTTtRQUV0QyxTQUFTO1FBQ1QsTUFBTUssWUFBWSxNQUFNQyxvQkFBb0JQLE9BQU96SSxFQUFFLEVBQUU0RztTQUd2RDhCLDhCQUFBQSxVQUFVSSxlQUFlLGNBQXpCSixrREFBQUEsaUNBQUFBLFdBQTRCSyxXQUFXLE9BQU87UUFFOUN4SSxRQUFRb0UsR0FBRyxDQUFDLGtCQUFrQm9FLFVBQVUzSCxNQUFNO0lBQ2hELEVBQUUsT0FBT2YsT0FBWTtZQUVuQnFJO1FBREFuSSxRQUFRRixLQUFLLENBQUMsYUFBYUE7U0FDM0JxSSw4QkFBQUEsVUFBVUksZUFBZSxjQUF6Qkosa0RBQUFBLGlDQUFBQSxXQUE0QixFQUFFLEVBQUUsT0FBT3JJLE1BQU1DLE9BQU8sSUFBSTtJQUMxRDtBQUNGLEVBQUU7QUFZRjs7O0NBR0MsR0FDTSxNQUFNMkksdUJBQXVCLElBQXlCO1FBQzNEQyxTQUFTLEVBQUU7UUFDWGhGLFNBQVM7UUFDVDdELE9BQU87UUFDUDhJLFNBQVM7SUFDWCxHQUFHO0FBRUg7Ozs7O0NBS0MsR0FDTSxNQUFNQyw0QkFBNEIsZUFDdkN6QyxRQUNBMEM7UUFDQXBELDRFQUFvQjtJQUVwQixJQUFJLENBQUNVLFFBQVE7UUFDWDBDLFNBQVM7WUFBRWhKLE9BQU87UUFBUTtRQUMxQjtJQUNGO0lBRUEsSUFBSTtRQUNGZ0osU0FBUztZQUFFbkYsU0FBUztZQUFNN0QsT0FBTztRQUFLO1FBRXRDLE1BQU15RyxjQUFjLE1BQU1kLGlCQUFpQkM7UUFDM0NvRCxTQUFTO1lBQ1BILFNBQVNwQztZQUNUNUMsU0FBUztRQUNYO0lBQ0YsRUFBRSxPQUFPb0YsS0FBVTtRQUNqQi9JLFFBQVFGLEtBQUssQ0FBQyxhQUFhaUo7UUFDM0JELFNBQVM7WUFDUGhKLE9BQU87WUFDUDZELFNBQVM7UUFDWDtJQUNGO0FBQ0YsRUFBRTtBQUlGOzs7O0NBSUMsR0FDTSxNQUFNcUYseUJBQXlCLE9BQ3BDRixVQUNBMUM7SUFFQSxNQUFNeUMsMEJBQTBCekMsUUFBUTBDLFVBQVU7QUFDcEQsRUFBRTtBQWNGOzs7OztDQUtDLEdBQ00sTUFBTUcscUNBQXFDLE9BQ2hEL0ksU0FDQVo7SUFFQSxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNUCxvREFBUUEsQ0FBQ2tLLGdCQUFnQixDQUFDaEo7UUFFakQsSUFBSVgsU0FBU0ksSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztZQUM5QixPQUFPTCxTQUFTSSxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO1FBQ2pDLE9BQU87WUFDTEssUUFBUUYsS0FBSyxDQUFDLGFBQWFQLFNBQVNJLElBQUksQ0FBQ0ksT0FBTztZQUNoRFQsYUFBYVEsS0FBSyxDQUFDO1lBQ25CLE9BQU8sRUFBRTtRQUNYO0lBQ0YsRUFBRSxPQUFPQSxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyxhQUFhQTtRQUMzQlIsYUFBYVEsS0FBSyxDQUFDO1FBQ25CLE9BQU8sRUFBRTtJQUNYO0FBQ0YsRUFBRTtBQUVGOzs7OztDQUtDLEdBQ00sTUFBTXFKLHFCQUFxQixPQUNoQ0MsU0FDQTlKO0lBRUEsSUFBSThKLFFBQVF2SSxNQUFNLEtBQUssR0FBRztRQUN4QixPQUFPLElBQUl3STtJQUNiO0lBRUEsSUFBSTtRQUNGLE1BQU0sRUFBRUMsU0FBUyxFQUFFLEdBQUcsTUFBTSx1SkFBTztRQUNuQ3RKLFFBQVFvRSxHQUFHLENBQUMsdUJBQXVCZ0Y7UUFDbkNwSixRQUFRb0UsR0FBRyxDQUFDLFlBQVksR0FBMEIsT0FBdkJrRixVQUFVQyxZQUFZLEVBQUM7UUFFbEQsdUJBQXVCO1FBQ3ZCLE1BQU1DLGlCQUFpQixJQUFJekUsUUFBUSxDQUFDMEUsR0FBR0M7WUFDckN4QyxXQUFXLElBQU13QyxPQUFPLElBQUkxRCxNQUFNLFVBQVU7UUFDOUM7UUFFQSxNQUFNMkQsYUFBYUwsVUFBVU0scUJBQXFCLENBQUNSO1FBQ25ELE1BQU03SixXQUFXLE1BQU13RixRQUFROEUsSUFBSSxDQUFDO1lBQUNGO1lBQVlIO1NBQWU7UUFFaEUsTUFBTU0sWUFBWSxJQUFJVDtRQUN0QnJKLFFBQVFvRSxHQUFHLENBQUMsVUFBVTdFO1FBRXRCLElBQUlBLFNBQVNJLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7WUFDOUIsTUFBTUQsT0FBT0osU0FBU0ksSUFBSSxDQUFDQSxJQUFJO1lBQy9CLGNBQWM7WUFDZCxJQUFLLE1BQU15RyxVQUFVekcsS0FBTTtnQkFDekIsTUFBTW9LLGNBQWNwSyxJQUFJLENBQUN5RyxPQUFPO2dCQUNoQyxJQUFJMkQsYUFBYTtvQkFDZixNQUFNdEksY0FBY3NJLFlBQVl0SSxXQUFXLElBQUk7b0JBQy9DLE1BQU1FLGtCQUFrQm9JLFlBQVlwSSxlQUFlLElBQUk7b0JBQ3ZELHdDQUF3QztvQkFDeEMsMkNBQTJDO29CQUMzQyxzQkFBc0I7b0JBQ3RCLE1BQU1xSSxrQkFBa0J2SSxjQUFjRTtvQkFDdENtSSxVQUFVRyxHQUFHLENBQUN4QyxPQUFPckIsU0FBUzBCLEtBQUtvQyxHQUFHLENBQUMsR0FBR0Y7Z0JBQzVDLE9BQU87b0JBQ0xGLFVBQVVHLEdBQUcsQ0FBQ3hDLE9BQU9yQixTQUFTO2dCQUNoQztZQUNGO1lBRUEsZ0JBQWdCO1lBQ2hCZ0QsUUFBUTlCLE9BQU8sQ0FBQzZDLENBQUFBO2dCQUNkLElBQUksQ0FBQ0wsVUFBVU0sR0FBRyxDQUFDRCxNQUFNO29CQUN2QkwsVUFBVUcsR0FBRyxDQUFDRSxLQUFLO2dCQUNyQjtZQUNGO1lBRUEsT0FBT0w7UUFDVCxPQUFPO1lBQ0wsa0NBQWtDO1lBQ2xDVixRQUFROUIsT0FBTyxDQUFDNkMsQ0FBQUEsTUFBT0wsVUFBVUcsR0FBRyxDQUFDRSxLQUFLO1lBQzFDbkssUUFBUXFLLElBQUksQ0FBQyxxQkFBcUI5SyxTQUFTSSxJQUFJLENBQUNJLE9BQU87WUFDdkQsT0FBTytKO1FBQ1Q7SUFDRixFQUFFLE9BQU9oSyxPQUFZO1lBV1JBLGlCQUdBQTtRQWJYRSxRQUFRRixLQUFLLENBQUMsZUFBZUE7UUFFN0Isa0JBQWtCO1FBQ2xCLE1BQU1nSyxZQUFZLElBQUlUO1FBQ3RCRCxRQUFROUIsT0FBTyxDQUFDNkMsQ0FBQUEsTUFBT0wsVUFBVUcsR0FBRyxDQUFDRSxLQUFLO1FBRTFDLGlCQUFpQjtRQUNqQixJQUFJckssTUFBTUMsT0FBTyxLQUFLLFFBQVE7WUFDNUJDLFFBQVFxSyxJQUFJLENBQUM7UUFDYixtQkFBbUI7UUFDckIsT0FBTyxJQUFJdkssRUFBQUEsa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sc0NBQUFBLGdCQUFnQnlFLE1BQU0sS0FBSSxLQUFLO1lBQ3hDdkUsUUFBUXFLLElBQUksQ0FBQztRQUNiLHlCQUF5QjtRQUMzQixPQUFPLElBQUl2SyxFQUFBQSxtQkFBQUEsTUFBTVAsUUFBUSxjQUFkTyx1Q0FBQUEsaUJBQWdCeUUsTUFBTSxNQUFLLEtBQUs7WUFDekMsZUFBZTtZQUNmakYsYUFBYXdCLE9BQU8sQ0FBQztRQUN2QjtRQUNBLHVCQUF1QjtRQUV2QixPQUFPZ0o7SUFDVDtBQUNGLEVBQUU7QUFFRjs7Ozs7Q0FLQyxHQUNNLE1BQU1RLHdCQUF3QixDQUNuQ0Msa0JBQ0FDO0lBRUEsSUFBSUQsaUJBQWlCMUosTUFBTSxLQUFLLEdBQUcsT0FBTztJQUUxQyxNQUFNNEosd0JBQXdCRixpQkFBaUJ2SixHQUFHLENBQUMwSixDQUFBQSxZQUNqREYsaUJBQWlCRyxHQUFHLENBQUNELGNBQWM7SUFHckMsT0FBTzVDLEtBQUs4QyxHQUFHLElBQUlIO0FBQ3JCLEVBQUU7QUFFRjs7Ozs7O0NBTUMsR0FDTSxNQUFNSSx1QkFBdUIsQ0FDbENDLHNCQUNBUCxrQkFDQUM7SUFFQSxJQUFJTSx5QkFBeUIsWUFBWUEseUJBQXlCLGNBQWM7UUFDOUUsT0FBTztZQUNMQyxPQUFPO1lBQ1BDLE9BQU9WLHNCQUFzQkMsa0JBQWtCQztRQUNqRDtJQUNGO0lBQ0EsT0FBTztRQUFFTyxPQUFPO1FBQUlDLE9BQU87SUFBRTtBQUMvQixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvY2xhc3NVdGlscy50cz83NjQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdldE5vdGlmaWNhdGlvbiB9IGZyb20gJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJztcbmltcG9ydCB7IE1vZGFsIH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgeyBjbGFzc0FwaSB9IGZyb20gJy4uLy4uLy4uL2xpYi9hcGkvY2xhc3MnO1xuaW1wb3J0IHsgc3R1ZGVudEFwaSB9IGZyb20gJy4uLy4uLy4uL2xpYi9hcGkvc3R1ZGVudCc7XG5pbXBvcnQgeyBzY2hvb2xBcGkgfSBmcm9tICcuLi8uLi8uLi9saWIvYXBpL3NjaG9vbCc7XG5pbXBvcnQgeyBDbGFzc0luZm8gfSBmcm9tICcuL3R5cGVzJztcblxuLyoqXG4gKiDlrabmoKHmjqXlj6NcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBTY2hvb2wge1xuICBpZDogbnVtYmVyO1xuICBzY2hvb2xOYW1lOiBzdHJpbmc7XG4gIHByb3ZpbmNlOiBzdHJpbmc7XG4gIGNpdHk6IHN0cmluZztcbiAgZGlzdHJpY3Q6IHN0cmluZztcbiAgc3RhdHM/OiB7XG4gICAgY2xhc3NDb3VudDogbnVtYmVyO1xuICAgIHN0dWRlbnRDb3VudDogbnVtYmVyO1xuICAgIHRlYWNoZXJDb3VudDogbnVtYmVyO1xuICB9O1xuICBqb2luVGltZT86IHN0cmluZztcbn1cblxuLyoqXG4gKiDnj63nuqfmjqXlj6NcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBDbGFzcyB7XG4gIGlkOiBudW1iZXI7XG4gIGNsYXNzTmFtZTogc3RyaW5nO1xuICBzY2hvb2xJZDogbnVtYmVyO1xufVxuXG4vKipcbiAqIOePree6p+euoeeQhuebuOWFs+W3peWFt+WHveaVsFxuICovXG5cbi8qKlxuICog57yW6L6R54+t57qn5L+h5oGvXG4gKiBAcGFyYW0gY2xhc3NJbmZvIOePree6p+S/oeaBr1xuICogQHBhcmFtIHZhbHVlcyDmm7TmlrDnmoTlgLxcbiAqIEByZXR1cm5zIFByb21pc2U8Ym9vbGVhbj5cbiAqL1xuZXhwb3J0IGNvbnN0IHVwZGF0ZUNsYXNzSW5mbyA9IGFzeW5jIChcbiAgY2xhc3NJbmZvOiBDbGFzc0luZm8sXG4gIHZhbHVlczogeyBjbGFzc05hbWU6IHN0cmluZyB9XG4pOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLnVwZGF0ZUNsYXNzU2ltcGxlKHtcbiAgICAgIGlkOiBjbGFzc0luZm8uaWQsXG4gICAgICBjbGFzc05hbWU6IHZhbHVlcy5jbGFzc05hbWVcbiAgICB9KTtcblxuICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+ePree6p+S/oeaBr+abtOaWsOaIkOWKnycpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+abtOaWsOePree6p+S/oeaBr+Wksei0pScpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+abtOaWsOePree6p+S/oeaBr+Wksei0pTonLCBlcnJvcik7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG4gICAgbm90aWZpY2F0aW9uLmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfmm7TmlrDnj63nuqfkv6Hmga/lpLHotKUnKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG5cbi8qKlxuICog5Yig6Zmk54+t57qnXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHJldHVybnMgUHJvbWlzZTxib29sZWFuPlxuICovXG5leHBvcnQgY29uc3QgZGVsZXRlQ2xhc3MgPSBhc3luYyAoY2xhc3NJZDogbnVtYmVyKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG4gICAgXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc0FwaS5kZWxldGVDbGFzcyhjbGFzc0lkKTtcblxuICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+ePree6p+WIoOmZpOaIkOWKnycpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+WIoOmZpOePree6p+Wksei0pScpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOePree6p+Wksei0pTonLCBlcnJvcik7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG4gICAgbm90aWZpY2F0aW9uLmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfliKDpmaTnj63nuqflpLHotKUnKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG5cbi8qKlxuICog6L2s56e754+t57qnXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHBhcmFtIG5ld1RlYWNoZXJJZCDmlrDmlZnluIhJRFxuICogQHBhcmFtIHRyYW5zZmVyVHlwZSDovaznp7vnsbvlnotcbiAqIEByZXR1cm5zIFByb21pc2U8Ym9vbGVhbj5cbiAqL1xuZXhwb3J0IGNvbnN0IHRyYW5zZmVyQ2xhc3MgPSBhc3luYyAoXG4gIGNsYXNzSWQ6IG51bWJlcixcbiAgbmV3VGVhY2hlcklkOiBudW1iZXIsXG4gIHRyYW5zZmVyVHlwZTogc3RyaW5nXG4pOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLnRyYW5zZmVyQ2xhc3MoY2xhc3NJZCwgbmV3VGVhY2hlcklkLCB0cmFuc2ZlclR5cGUpO1xuXG4gICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn54+t57qn6L2s56e75oiQ5YqfJyk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn6L2s56e754+t57qn5aSx6LSlJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcign6L2s56e754+t57qn5aSx6LSlOicsIGVycm9yKTtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+i9rOenu+ePree6p+Wksei0pScpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufTtcblxuLyoqXG4gKiDnlJ/miJDnj63nuqfpgoDor7fnoIFcbiAqIEBwYXJhbSBjbGFzc0lkIOePree6p0lEXG4gKiBAcmV0dXJucyBQcm9taXNlPHN0cmluZyB8IG51bGw+XG4gKi9cbmV4cG9ydCBjb25zdCBnZW5lcmF0ZUNsYXNzSW52aXRlQ29kZSA9IGFzeW5jIChjbGFzc0lkOiBudW1iZXIpOiBQcm9taXNlPHN0cmluZyB8IG51bGw+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLmdlbmVyYXRlSW52aXRlQ29kZShjbGFzc0lkKTtcblxuICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xuICAgICAgY29uc3QgaW52aXRlQ29kZSA9IHJlc3BvbnNlLmRhdGEuZGF0YS5pbnZpdGVDb2RlO1xuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+mCgOivt+eggeeUn+aIkOaIkOWKnycpO1xuICAgICAgcmV0dXJuIGludml0ZUNvZGU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+eUn+aIkOmCgOivt+eggeWksei0pScpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcign55Sf5oiQ6YKA6K+356CB5aSx6LSlOicsIGVycm9yKTtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+eUn+aIkOmCgOivt+eggeWksei0pScpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4vKipcbiAqIOaQnOe0ouaVmeW4iFxuICogQHBhcmFtIHBob25lIOaVmeW4iOaJi+acuuWPt1xuICogQHJldHVybnMgUHJvbWlzZTxhbnkgfCBudWxsPlxuICovXG5leHBvcnQgY29uc3Qgc2VhcmNoVGVhY2hlckJ5UGhvbmUgPSBhc3luYyAocGhvbmU6IHN0cmluZyk6IFByb21pc2U8YW55IHwgbnVsbD4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkuc2VhcmNoVGVhY2hlckJ5UGhvbmUocGhvbmUpO1xuXG4gICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+acquaJvuWIsOivpeaVmeW4iCcpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcign5pCc57Si5pWZ5biI5aSx6LSlOicsIGVycm9yKTtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+aQnOe0ouaVmeW4iOWksei0pScpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4vKipcbiAqIOWvvOWHuuWtpueUn+S/oeaBr1xuICogQHBhcmFtIHN0dWRlbnRzIOWtpueUn+WIl+ihqFxuICogQHBhcmFtIGNsYXNzTmFtZSDnj63nuqflkI3np7BcbiAqL1xuZXhwb3J0IGNvbnN0IGV4cG9ydFN0dWRlbnRzRGF0YSA9IChzdHVkZW50czogYW55W10sIGNsYXNzTmFtZTogc3RyaW5nKTogdm9pZCA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG4gICAgXG4gICAgaWYgKHN0dWRlbnRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgbm90aWZpY2F0aW9uLndhcm5pbmcoJ+ayoeacieWtpueUn+aVsOaNruWPr+S7peWvvOWHuicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIOWHhuWkh+WvvOWHuuaVsOaNrlxuICAgIGNvbnN0IGV4cG9ydERhdGEgPSBzdHVkZW50cy5tYXAoKHN0dWRlbnQsIGluZGV4KSA9PiAoe1xuICAgICAg5bqP5Y+3OiBpbmRleCArIDEsXG4gICAgICDlrablj7c6IHN0dWRlbnQuc3R1ZGVudE51bWJlciB8fCAnJyxcbiAgICAgIOWnk+WQjTogc3R1ZGVudC5uaWNrTmFtZSB8fCAnJyxcbiAgICAgIOaAu+enr+WIhjogc3R1ZGVudC50b3RhbFBvaW50cyB8fCAwLFxuICAgICAg5Y+v55So56ev5YiGOiBzdHVkZW50LmF2YWlsYWJsZVBvaW50cyB8fCAwLFxuICAgICAg5b2T5YmN5qih5p2/OiBzdHVkZW50LmN1cnJlbnRUZW1wbGF0ZT8udGVtcGxhdGVOYW1lIHx8ICfml6AnXG4gICAgfSkpO1xuXG4gICAgLy8g6L2s5o2i5Li6Q1NW5qC85byPXG4gICAgY29uc3QgaGVhZGVycyA9IE9iamVjdC5rZXlzKGV4cG9ydERhdGFbMF0pO1xuICAgIGNvbnN0IGNzdkNvbnRlbnQgPSBbXG4gICAgICBoZWFkZXJzLmpvaW4oJywnKSxcbiAgICAgIC4uLmV4cG9ydERhdGEubWFwKHJvdyA9PiBoZWFkZXJzLm1hcChoZWFkZXIgPT4gYFwiJHtyb3dbaGVhZGVyIGFzIGtleW9mIHR5cGVvZiByb3ddfVwiYCkuam9pbignLCcpKVxuICAgIF0uam9pbignXFxuJyk7XG5cbiAgICAvLyDliJvlu7rkuIvovb3pk77mjqVcbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoWydcXHVGRUZGJyArIGNzdkNvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L2NzdjtjaGFyc2V0PXV0Zi04OycgfSk7XG4gICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgIFxuICAgIGxpbmsuc2V0QXR0cmlidXRlKCdocmVmJywgdXJsKTtcbiAgICBsaW5rLnNldEF0dHJpYnV0ZSgnZG93bmxvYWQnLCBgJHtjbGFzc05hbWV9X+WtpueUn+S/oeaBr18ke25ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfS5jc3ZgKTtcbiAgICBsaW5rLnN0eWxlLnZpc2liaWxpdHkgPSAnaGlkZGVuJztcbiAgICBcbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspO1xuICAgIGxpbmsuY2xpY2soKTtcbiAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspO1xuXG4gICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WtpueUn+S/oeaBr+WvvOWHuuaIkOWKnycpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWHuuWtpueUn+S/oeaBr+Wksei0pTonLCBlcnJvcik7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG4gICAgbm90aWZpY2F0aW9uLmVycm9yKCflr7zlh7rlrabnlJ/kv6Hmga/lpLHotKUnKTtcbiAgfVxufTtcblxuLyoqXG4gKiDkvb/nlKhBUEnlr7zlh7rlrabnlJ/kv6Hmga9cbiAqIEBwYXJhbSBjbGFzc0lkIOePree6p0lEXG4gKiBAcmV0dXJucyBQcm9taXNlPGJvb2xlYW4+XG4gKi9cbmV4cG9ydCBjb25zdCBleHBvcnRTdHVkZW50c1ZpYUFQSSA9IGFzeW5jIChjbGFzc0lkOiBudW1iZXIpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcbiAgICBjb25zdCBoaWRlTG9hZGluZyA9IG5vdGlmaWNhdGlvbi5sb2FkaW5nKCfmraPlnKjlr7zlh7rlrabnlJ/kv6Hmga8uLi4nKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc3R1ZGVudEFwaS5leHBvcnRTdHVkZW50cyhjbGFzc0lkKTtcblxuICAgIGlmIChoaWRlTG9hZGluZykge1xuICAgICAgaGlkZUxvYWRpbmcuY2xvc2UoKTtcbiAgICB9XG5cbiAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcbiAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCflr7zlh7rlrabnlJ/miJDlip8nKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gZWxzZSB7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICflr7zlh7rlrabnlJ/lpLHotKUnKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCflr7zlh7rlrabnlJ/lpLHotKU6JywgZXJyb3IpO1xuICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xuICAgIG5vdGlmaWNhdGlvbi5lcnJvcihlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn5a+85Ye65a2m55Sf5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG4vKipcbiAqIOmqjOivgeePree6p+WQjeensFxuICogQHBhcmFtIGNsYXNzTmFtZSDnj63nuqflkI3np7BcbiAqIEByZXR1cm5zIHsgaXNWYWxpZDogYm9vbGVhbjsgbWVzc2FnZT86IHN0cmluZyB9XG4gKi9cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUNsYXNzTmFtZSA9IChjbGFzc05hbWU6IHN0cmluZyk6IHsgaXNWYWxpZDogYm9vbGVhbjsgbWVzc2FnZT86IHN0cmluZyB9ID0+IHtcbiAgaWYgKCFjbGFzc05hbWUgfHwgY2xhc3NOYW1lLnRyaW0oKS5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4geyBpc1ZhbGlkOiBmYWxzZSwgbWVzc2FnZTogJ+ePree6p+WQjeensOS4jeiDveS4uuepuicgfTtcbiAgfVxuXG4gIGlmIChjbGFzc05hbWUubGVuZ3RoID4gNTApIHtcbiAgICByZXR1cm4geyBpc1ZhbGlkOiBmYWxzZSwgbWVzc2FnZTogJ+ePree6p+WQjeensOS4jeiDvei2hei/hzUw5Liq5a2X56ymJyB9O1xuICB9XG5cbiAgLy8g5qOA5p+l54m55q6K5a2X56ymXG4gIGNvbnN0IGludmFsaWRDaGFycyA9IC9bPD46XCIvXFxcXHw/Kl0vO1xuICBpZiAoaW52YWxpZENoYXJzLnRlc3QoY2xhc3NOYW1lKSkge1xuICAgIHJldHVybiB7IGlzVmFsaWQ6IGZhbHNlLCBtZXNzYWdlOiAn54+t57qn5ZCN56ew5LiN6IO95YyF5ZCr54m55q6K5a2X56ymIDwgPiA6IFwiIC8gXFxcXCB8ID8gKicgfTtcbiAgfVxuXG4gIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTtcbn07XG5cbi8qKlxuICog57yW6L6R54+t57qn5L+h5oGv77yI5aKe5by654mI77yJXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHBhcmFtIHZhbHVlcyDnj63nuqfkv6Hmga9cbiAqIEByZXR1cm5zIFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBkYXRhPzogYW55IH0+XG4gKi9cbmV4cG9ydCBjb25zdCBlZGl0Q2xhc3NJbmZvID0gYXN5bmMgKFxuICBjbGFzc0lkOiBudW1iZXIsXG4gIHZhbHVlczogeyBjbGFzc05hbWU6IHN0cmluZzsgZ3JhZGU/OiBzdHJpbmcgfVxuKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnkgfT4gPT4ge1xuICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcblxuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCflvIDlp4vnvJbovpHnj63nuqc6Jywge1xuICAgICAgY2xhc3NJZDogY2xhc3NJZCxcbiAgICAgIHZhbHVlczogdmFsdWVzXG4gICAgfSk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLnVwZGF0ZUNsYXNzKGNsYXNzSWQsIHtcbiAgICAgIGNsYXNzTmFtZTogdmFsdWVzLmNsYXNzTmFtZSxcbiAgICAgIGdyYWRlOiB2YWx1ZXMuZ3JhZGUgfHwgJydcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCfnvJbovpHnj63nuqdBUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICBjb25zb2xlLmxvZygn57yW6L6R54+t57qn5oiQ5YqfJyk7XG4gICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn57yW6L6R54+t57qn5oiQ5YqfJyk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhLmRhdGEgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc29sZS5lcnJvcign57yW6L6R54+t57qn5aSx6LSlIC0gQVBJ6L+U5Zue6ZSZ6K+vOicsIHtcbiAgICAgICAgY29kZTogcmVzcG9uc2UuZGF0YS5jb2RlLFxuICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5kYXRhLm1lc3NhZ2UsXG4gICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGFcbiAgICAgIH0pO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn57yW6L6R54+t57qn5aSx6LSlJyk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+e8lui+keePree6p+Wksei0pSAtIOivt+axguW8guW4uDonLCB7XG4gICAgICBlcnJvcjogZXJyb3IsXG4gICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzXG4gICAgfSk7XG5cbiAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+e8lui+keePree6p+Wksei0pe+8jOivt+eojeWQjumHjeivlScpO1xuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XG4gIH1cbn07XG5cbi8qKlxuICog56e75Ye65Y2P5Yqp5pWZ5biIXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHJldHVybnMgUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnkgfT5cbiAqL1xuZXhwb3J0IGNvbnN0IHJlbW92ZUFzc2lzdGFudFRlYWNoZXIgPSBhc3luYyAoY2xhc3NJZDogbnVtYmVyKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGRhdGE/OiBhbnkgfT4gPT4ge1xuICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkudXBkYXRlQ2xhc3MoY2xhc3NJZCwge1xuICAgICAgYXNzaXN0YW50VGVhY2hlcklkOiAwXG4gICAgfSk7XG5cbiAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcbiAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfnp7vlh7rljY/liqnmlZnluIjmiJDlip8nKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEuZGF0YSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfnp7vlh7rljY/liqnmlZnluIjlpLHotKUnKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcign56e75Ye65Y2P5Yqp5pWZ5biI5aSx6LSlOicsIGVycm9yKTtcbiAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+enu+WHuuWNj+WKqeaVmeW4iOWksei0pScpO1xuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XG4gIH1cbn07XG5cbi8qKlxuICog5Yig6Zmk54+t57qn77yI5aKe5by654mI77yM5YyF5ZCr5a2m55Sf5qOA5p+l77yJXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHBhcmFtIHN0dWRlbnRzIOWtpueUn+WIl+ihqFxuICogQHBhcmFtIG9uU3VjY2VzcyDmiJDlip/lm57osINcbiAqIEByZXR1cm5zIFByb21pc2U8Ym9vbGVhbj5cbiAqL1xuZXhwb3J0IGNvbnN0IGRlbGV0ZUNsYXNzV2l0aENoZWNrID0gYXN5bmMgKFxuICBjbGFzc0lkOiBudW1iZXIsXG4gIHN0dWRlbnRzOiBhbnlbXSxcbiAgb25TdWNjZXNzPzogKCkgPT4gdm9pZFxuKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xuXG4gIC8vIOWFiOajgOafpeaYr+WQpuacieWtpueUn1xuICBpZiAoc3R1ZGVudHMubGVuZ3RoID4gMCkge1xuICAgIE1vZGFsLndhcm5pbmcoe1xuICAgICAgdGl0bGU6ICfml6Dms5XliKDpmaTnj63nuqcnLFxuICAgICAgY29udGVudDogYOivpeePree6p+i/mOaciSAke3N0dWRlbnRzLmxlbmd0aH0g5ZCN5a2m55Sf77yM6K+35YWI56e75Ye65omA5pyJ5a2m55Sf5ZCO5YaN5Yig6Zmk54+t57qn44CCYCxcbiAgICAgIG9rVGV4dDogJ+ehruWumidcbiAgICB9KTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICBNb2RhbC5jb25maXJtKHtcbiAgICAgIHRpdGxlOiAn56Gu6K6k5Yig6Zmk54+t57qnJyxcbiAgICAgIGNvbnRlbnQ6ICfliKDpmaTlkI7ml6Dms5XmgaLlpI3vvIznoa7lrpropoHliKDpmaTov5nkuKrnj63nuqflkJfvvJ8nLFxuICAgICAgb2tUZXh0OiAn56Gu5a6a5Yig6ZmkJyxcbiAgICAgIG9rVHlwZTogJ2RhbmdlcicsXG4gICAgICBjYW5jZWxUZXh0OiAn5Y+W5raIJyxcbiAgICAgIGNlbnRlcmVkOiB0cnVlLFxuICAgICAgb25PazogYXN5bmMgKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkuZGVsZXRlQ2xhc3MoY2xhc3NJZCk7XG5cbiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliKDpmaTnj63nuqfmiJDlip8nKTtcbiAgICAgICAgICAgIG9uU3VjY2Vzcz8uKCk7XG4gICAgICAgICAgICByZXNvbHZlKHRydWUpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfliKDpmaTnj63nuqflpLHotKUnKTtcbiAgICAgICAgICAgIHJlc29sdmUoZmFsc2UpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOePree6p+Wksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlKSB7XG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2UuZGF0YS5tZXNzYWdlKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliKDpmaTnj63nuqflpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmVzb2x2ZShmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBvbkNhbmNlbDogKCkgPT4ge1xuICAgICAgICByZXNvbHZlKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfSk7XG59O1xuXG4vKipcbiAqIOWtpuagoeaVsOaNrue8k+WtmOaOpeWPo1xuICovXG5pbnRlcmZhY2UgU2Nob29sQ2FjaGUge1xuICBkYXRhOiBTY2hvb2xbXTtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG59XG5cbi8vIOWtpuagoeaVsOaNrue8k+WtmFxubGV0IHNjaG9vbHNDYWNoZTogU2Nob29sQ2FjaGUgfCBudWxsID0gbnVsbDtcbmNvbnN0IFNDSE9PTF9DQUNIRV9EVVJBVElPTiA9IDUgKiA2MCAqIDEwMDA7IC8vIDXliIbpkp/nvJPlrZhcblxuLyoqXG4gKiDojrflj5bnlKjmiLflhbPogZTnmoTlrabmoKHliJfooajvvIjmlK/mjIHnvJPlrZjvvIlcbiAqIEBwYXJhbSB1c2VDYWNoZSDmmK/lkKbkvb/nlKjnvJPlrZjvvIzpu5jorqR0cnVlXG4gKiBAcmV0dXJucyBQcm9taXNlPFNjaG9vbFtdPlxuICovXG5leHBvcnQgY29uc3QgZmV0Y2hVc2VyU2Nob29scyA9IGFzeW5jICh1c2VDYWNoZTogYm9vbGVhbiA9IHRydWUpOiBQcm9taXNlPFNjaG9vbFtdPiA9PiB7XG4gIHRyeSB7XG4gICAgLy8g5qOA5p+l57yT5a2YXG4gICAgaWYgKHVzZUNhY2hlICYmIHNjaG9vbHNDYWNoZSAmJiBEYXRlLm5vdygpIC0gc2Nob29sc0NhY2hlLnRpbWVzdGFtcCA8IFNDSE9PTF9DQUNIRV9EVVJBVElPTikge1xuICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOWtpuagoeWIl+ihqOe8k+WtmCcpO1xuICAgICAgcmV0dXJuIHNjaG9vbHNDYWNoZS5kYXRhO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCc9PT0g5byA5aeL6I635Y+W55So5oi35a2m5qCh5YiX6KGoID09PScpO1xuXG4gICAgLy8g5LuObG9jYWxTdG9yYWdl6I635Y+W55So5oi35L+h5oGvXG4gICAgY29uc3QgdXNlckluZm8gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xuXG4gICAgaWYgKCF1c2VySW5mbykge1xuICAgICAgY29uc29sZS5lcnJvcign5pyq5om+5Yiw55So5oi35L+h5oGv77yM6K+35qOA5p+l55m75b2V54q25oCBJyk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ+eUqOaIt+acqueZu+W9lScpO1xuICAgIH1cblxuICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKHVzZXJJbmZvKTtcbiAgICBjb25zb2xlLmxvZygn6Kej5p6Q55qE55So5oi35L+h5oGvOicsIHVzZXIpO1xuXG4gICAgLy8g5qC55o2u6aG555uu5Lit5YW25LuW57uE5Lu255qE5L2/55So5pa55byP77yM5bCd6K+V5aSa56eN5Y+v6IO955qE55So5oi3SUTlrZfmrrXlkI1cbiAgICBjb25zdCB1c2VySWQgPSB1c2VyLmlkIHx8IHVzZXIudXNlcklkIHx8IHVzZXIudGVhY2hlcklkO1xuXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+eUqOaIt+S/oeaBr+S4reacquaJvuWIsElE5a2X5q61OicsIHVzZXIpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfnlKjmiLdJROS4jeWtmOWcqCcpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfkvb/nlKjnmoTnlKjmiLdJRDonLCB1c2VySWQpO1xuXG4gICAgLy8g5L2/55So6aG555uu5Lit5bey5pyJ55qEQVBJ5Ye95pWwXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzY2hvb2xBcGkuZ2V0VXNlclNjaG9vbHMoKTtcbiAgICBjb25zb2xlLmxvZygn6I635Y+W5a2m5qCh5pWw5o2uOicsIHJlc3BvbnNlKTtcblxuICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhLmRhdGEpIHtcbiAgICAgIC8vIOehruS/neaVsOaNruaYr+aVsOe7hFxuICAgICAgY29uc3Qgc2Nob29sc0RhdGEgPSBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkgPyByZXNwb25zZS5kYXRhLmRhdGEgOiBbXTtcbiAgICAgIGNvbnNvbGUubG9nKCfmiJDlip/ojrflj5blrabmoKHliJfooag6Jywgc2Nob29sc0RhdGEpO1xuXG4gICAgICAvLyDmm7TmlrDnvJPlrZhcbiAgICAgIHNjaG9vbHNDYWNoZSA9IHtcbiAgICAgICAgZGF0YTogc2Nob29sc0RhdGEsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfTtcblxuICAgICAgcmV0dXJuIHNjaG9vbHNEYXRhO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBUEnov5Tlm57plJnor686JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYOiOt+WPluWtpuagoeWIl+ihqOWksei0pTogJHtyZXNwb25zZS5kYXRhLm1zZyB8fCAn5pyq55+l6ZSZ6K+vJ31gKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign6I635Y+W5a2m5qCh5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuLyoqXG4gKiDmuIXpmaTlrabmoKHliJfooajnvJPlrZhcbiAqL1xuZXhwb3J0IGNvbnN0IGNsZWFyU2Nob29sc0NhY2hlID0gKCk6IHZvaWQgPT4ge1xuICBzY2hvb2xzQ2FjaGUgPSBudWxsO1xuICBjb25zb2xlLmxvZygn5bey5riF6Zmk5a2m5qCh5YiX6KGo57yT5a2YJyk7XG59O1xuXG4vKipcbiAqIOiOt+WPluWtpuagoee8k+WtmOS/oeaBr1xuICogQHJldHVybnMg57yT5a2Y5L+h5oGv5oiWbnVsbFxuICovXG5leHBvcnQgY29uc3QgZ2V0U2Nob29sc0NhY2hlSW5mbyA9ICgpID0+IHtcbiAgaWYgKCFzY2hvb2xzQ2FjaGUpIHJldHVybiBudWxsO1xuXG4gIGNvbnN0IGlzRXhwaXJlZCA9IERhdGUubm93KCkgLSBzY2hvb2xzQ2FjaGUudGltZXN0YW1wID49IFNDSE9PTF9DQUNIRV9EVVJBVElPTjtcbiAgcmV0dXJuIHtcbiAgICBkYXRhQ291bnQ6IHNjaG9vbHNDYWNoZS5kYXRhLmxlbmd0aCxcbiAgICB0aW1lc3RhbXA6IHNjaG9vbHNDYWNoZS50aW1lc3RhbXAsXG4gICAgaXNFeHBpcmVkLFxuICAgIHJlbWFpbmluZ1RpbWU6IGlzRXhwaXJlZCA/IDAgOiBTQ0hPT0xfQ0FDSEVfRFVSQVRJT04gLSAoRGF0ZS5ub3coKSAtIHNjaG9vbHNDYWNoZS50aW1lc3RhbXApXG4gIH07XG59O1xuXG4vKipcbiAqIOmihOWKoOi9veWtpuagoeaVsOaNrlxuICogQHBhcmFtIGRlbGF5IOW7tui/n+aXtumXtO+8iOavq+enku+8ie+8jOm7mOiupDEwMDBtc1xuICovXG5leHBvcnQgY29uc3QgcHJlbG9hZFNjaG9vbHNEYXRhID0gKGRlbGF5OiBudW1iZXIgPSAxMDAwKTogdm9pZCA9PiB7XG4gIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBmZXRjaFVzZXJTY2hvb2xzKCk7XG4gICAgICBjb25zb2xlLmxvZygn5a2m5qCh5pWw5o2u6aKE5Yqg6L295a6M5oiQJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WtpuagoeaVsOaNrumihOWKoOi9veWksei0pTonLCBlcnJvcik7XG4gICAgfVxuICB9LCBkZWxheSk7XG59O1xuXG4vKipcbiAqIOaVmeW4iOWtpuagoeeuoeeQhuebuOWFs+W3peWFt+WHveaVsFxuICovXG5cbi8vIOePree6p+WIl+ihqOe8k+WtmOaOpeWPo1xuaW50ZXJmYWNlIENsYXNzQ2FjaGUge1xuICBkYXRhOiBhbnlbXTtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIHNjaG9vbElkOiBudW1iZXI7XG59XG5cbi8vIOe8k+WtmOaMgee7reaXtumXtO+8mjXliIbpkp9cbmNvbnN0IFRFQUNIRVJfQ0FDSEVfRFVSQVRJT04gPSA1ICogNjAgKiAxMDAwO1xuXG4vLyDlhajlsYDnvJPlrZjlr7nosaFcbmxldCB0ZWFjaGVyQ2xhc3Nlc0NhY2hlOiB7IFtzY2hvb2xJZDogbnVtYmVyXTogQ2xhc3NDYWNoZSB9ID0ge307XG5cbi8vIOW3suenu+mZpO+8mmZldGNoVGVhY2hlclNjaG9vbHMgLSDor7fkvb/nlKggY2xhc3NTZXJ2aWNlLmdldFRlYWNoZXJTY2hvb2xzKClcblxuLy8g5bey56e76Zmk77yaZmV0Y2hUZWFjaGVyQ2xhc3NlcyAtIOivt+S9v+eUqCBjbGFzc1NlcnZpY2UuZ2V0U2Nob29sQ2xhc3NlcygpXG5cbi8vIOW3suenu+mZpO+8mnVwZGF0ZVRlYWNoZXJDbGFzc0NhY2hlIC0g6K+35L2/55SoIGNsYXNzU2VydmljZSDnmoTnvJPlrZjns7vnu59cblxuLy8g5bey56e76Zmk77yaY2xlYXJUZWFjaGVyQ2xhc3NDYWNoZSAtIOivt+S9v+eUqCBjbGFzc1NlcnZpY2Ug55qE57yT5a2Y57O757ufXG5cbi8qKlxuICog6LCD6K+V5Ye95pWw77ya6I635Y+W5b2T5YmN57yT5a2Y54q25oCBXG4gKi9cbmV4cG9ydCBjb25zdCBkZWJ1Z0NsYXNzQ2FjaGUgPSAoKSA9PiB7XG4gIGNvbnNvbGUubG9nKCc9PT0g54+t57qn57yT5a2Y6LCD6K+V5L+h5oGvID09PScpO1xuICBjb25zb2xlLmxvZygn57yT5a2Y5oyB57ut5pe26Ze0OicsIFRFQUNIRVJfQ0FDSEVfRFVSQVRJT04sICdtcycpO1xuICBjb25zb2xlLmxvZygn5b2T5YmN5pe26Ze0OicsIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSk7XG4gIGNvbnNvbGUubG9nKCfnvJPlrZjlhoXlrrk6Jyk7XG5cbiAgT2JqZWN0LmtleXModGVhY2hlckNsYXNzZXNDYWNoZSkuZm9yRWFjaChzY2hvb2xJZCA9PiB7XG4gICAgY29uc3QgY2FjaGUgPSB0ZWFjaGVyQ2xhc3Nlc0NhY2hlW051bWJlcihzY2hvb2xJZCldO1xuICAgIGNvbnN0IGlzRXhwaXJlZCA9IERhdGUubm93KCkgLSBjYWNoZS50aW1lc3RhbXAgPiBURUFDSEVSX0NBQ0hFX0RVUkFUSU9OO1xuICAgIGNvbnNvbGUubG9nKGAgIOWtpuagoSR7c2Nob29sSWR9OmAsIHtcbiAgICAgIOePree6p+aVsOmHjzogY2FjaGUuZGF0YS5sZW5ndGgsXG4gICAgICDnvJPlrZjml7bpl7Q6IG5ldyBEYXRlKGNhY2hlLnRpbWVzdGFtcCkudG9JU09TdHJpbmcoKSxcbiAgICAgIOaYr+WQpui/h+acnzogaXNFeHBpcmVkLFxuICAgICAg5Ymp5L2Z5pe26Ze0OiBpc0V4cGlyZWQgPyAn5bey6L+H5pyfJyA6IGAke01hdGgucm91bmQoKFRFQUNIRVJfQ0FDSEVfRFVSQVRJT04gLSAoRGF0ZS5ub3coKSAtIGNhY2hlLnRpbWVzdGFtcCkpIC8gMTAwMCl956eSYFxuICAgIH0pO1xuICB9KTtcbiAgY29uc29sZS5sb2coJz09PSDosIPor5Xkv6Hmga/nu5PmnZ8gPT09Jyk7XG59O1xuXG4vKipcbiAqIOiOt+WPluaVmeW4iOePree6p+e8k+WtmOeKtuaAgVxuICogQHBhcmFtIHNjaG9vbElkIOWtpuagoUlEXG4gKiBAcmV0dXJucyDnvJPlrZjkv6Hmga/miJZudWxsXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRUZWFjaGVyQ2xhc3NDYWNoZUluZm8gPSAoc2Nob29sSWQ6IG51bWJlcikgPT4ge1xuICBjb25zdCBjYWNoZSA9IHRlYWNoZXJDbGFzc2VzQ2FjaGVbc2Nob29sSWRdO1xuICBpZiAoIWNhY2hlKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCBpc0V4cGlyZWQgPSBEYXRlLm5vdygpIC0gY2FjaGUudGltZXN0YW1wID49IFRFQUNIRVJfQ0FDSEVfRFVSQVRJT047XG4gIHJldHVybiB7XG4gICAgc2Nob29sSWQ6IGNhY2hlLnNjaG9vbElkLFxuICAgIGRhdGFDb3VudDogY2FjaGUuZGF0YS5sZW5ndGgsXG4gICAgdGltZXN0YW1wOiBjYWNoZS50aW1lc3RhbXAsXG4gICAgaXNFeHBpcmVkLFxuICAgIHJlbWFpbmluZ1RpbWU6IGlzRXhwaXJlZCA/IDAgOiBURUFDSEVSX0NBQ0hFX0RVUkFUSU9OIC0gKERhdGUubm93KCkgLSBjYWNoZS50aW1lc3RhbXApXG4gIH07XG59O1xuXG4vKipcbiAqIOWkhOeQhuWtpuagoemAieaLqeeahOS4muWKoemAu+i+kVxuICogQHBhcmFtIHNjaG9vbCDpgInkuK3nmoTlrabmoKFcbiAqIEBwYXJhbSB0ZWFjaGVySWQg5pWZ5biISURcbiAqIEBwYXJhbSBjYWxsYmFja3Mg5Zue6LCD5Ye95pWw6ZuG5ZCIXG4gKi9cbmV4cG9ydCBjb25zdCBoYW5kbGVTY2hvb2xTZWxlY3Rpb24gPSBhc3luYyAoXG4gIHNjaG9vbDogU2Nob29sLFxuICB0ZWFjaGVySWQ6IG51bWJlcixcbiAgY2FsbGJhY2tzOiB7XG4gICAgb25TY2hvb2xTZWxlY3Q/OiAoc2Nob29sOiBTY2hvb2wpID0+IHZvaWQ7XG4gICAgb25DbGFzc2VzVXBkYXRlPzogKGNsYXNzZXM6IGFueVtdLCBsb2FkaW5nOiBib29sZWFuLCBlcnJvcjogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbiAgICBvbk1lbnVJdGVtQ2xpY2s/OiAoaXRlbU5hbWU6IHN0cmluZykgPT4gdm9pZDtcbiAgfVxuKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ+WkhOeQhuWtpuagoemAieaLqTonLCBzY2hvb2wuc2Nob29sTmFtZSk7XG5cbiAgICAvLyDpgJrnn6XlrabmoKHpgInmi6nlj5jljJZcbiAgICBjYWxsYmFja3Mub25TY2hvb2xTZWxlY3Q/LihzY2hvb2wpO1xuXG4gICAgLy8g5YiH5o2i5Yiw54+t57qn566h55CG6aG16Z2iXG4gICAgY2FsbGJhY2tzLm9uTWVudUl0ZW1DbGljaz8uKCfnj63nuqfnrqHnkIYnKTtcblxuICAgIC8vIOmAmuefpeW8gOWni+WKoOi9veePree6p+aVsOaNrlxuICAgIGNhbGxiYWNrcy5vbkNsYXNzZXNVcGRhdGU/LihbXSwgdHJ1ZSwgbnVsbCk7XG5cbiAgICAvLyDojrflj5bnj63nuqfliJfooahcbiAgICBjb25zdCBjbGFzc0xpc3QgPSBhd2FpdCBmZXRjaFRlYWNoZXJDbGFzc2VzKHNjaG9vbC5pZCwgdGVhY2hlcklkKTtcblxuICAgIC8vIOmAmuefpeePree6p+aVsOaNruabtOaWsOWujOaIkFxuICAgIGNhbGxiYWNrcy5vbkNsYXNzZXNVcGRhdGU/LihjbGFzc0xpc3QsIGZhbHNlLCBudWxsKTtcblxuICAgIGNvbnNvbGUubG9nKCflrabmoKHpgInmi6nlpITnkIblrozmiJDvvIznj63nuqfmlbDph486JywgY2xhc3NMaXN0Lmxlbmd0aCk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCflpITnkIblrabmoKHpgInmi6nlpLHotKU6JywgZXJyb3IpO1xuICAgIGNhbGxiYWNrcy5vbkNsYXNzZXNVcGRhdGU/LihbXSwgZmFsc2UsIGVycm9yLm1lc3NhZ2UgfHwgJ+iOt+WPluePree6p+WIl+ihqOWksei0pScpO1xuICB9XG59O1xuXG4vKipcbiAqIOWtpuagoemAieaLqeaooeaAgeahhueKtuaAgeeuoeeQhlxuICovXG5leHBvcnQgaW50ZXJmYWNlIFNjaG9vbE1vZGFsU3RhdGUge1xuICBzY2hvb2xzOiBTY2hvb2xbXTtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgZXJyb3I6IHN0cmluZyB8IG51bGw7XG4gIG1vdW50ZWQ6IGJvb2xlYW47XG59XG5cbi8qKlxuICog5Yid5aeL5YyW5a2m5qCh6YCJ5oup5qih5oCB5qGG54q25oCBXG4gKiBAcmV0dXJucyBTY2hvb2xNb2RhbFN0YXRlXG4gKi9cbmV4cG9ydCBjb25zdCBpbml0U2Nob29sTW9kYWxTdGF0ZSA9ICgpOiBTY2hvb2xNb2RhbFN0YXRlID0+ICh7XG4gIHNjaG9vbHM6IFtdLFxuICBsb2FkaW5nOiBmYWxzZSxcbiAgZXJyb3I6IG51bGwsXG4gIG1vdW50ZWQ6IGZhbHNlXG59KTtcblxuLyoqXG4gKiDlpITnkIblrabmoKHpgInmi6nmqKHmgIHmoYbnmoTmlbDmja7liqDovb1cbiAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAqIEBwYXJhbSBzZXRTdGF0ZSDnirbmgIHmm7TmlrDlh73mlbBcbiAqIEBwYXJhbSB1c2VDYWNoZSDmmK/lkKbkvb/nlKjnvJPlrZhcbiAqL1xuZXhwb3J0IGNvbnN0IGhhbmRsZVNjaG9vbE1vZGFsRGF0YUxvYWQgPSBhc3luYyAoXG4gIHVzZXJJZDogbnVtYmVyIHwgbnVsbCxcbiAgc2V0U3RhdGU6IChzdGF0ZTogUGFydGlhbDxTY2hvb2xNb2RhbFN0YXRlPikgPT4gdm9pZCxcbiAgdXNlQ2FjaGU6IGJvb2xlYW4gPSB0cnVlXG4pOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgaWYgKCF1c2VySWQpIHtcbiAgICBzZXRTdGF0ZSh7IGVycm9yOiAn55So5oi35pyq55m75b2VJyB9KTtcbiAgICByZXR1cm47XG4gIH1cblxuICB0cnkge1xuICAgIHNldFN0YXRlKHsgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG5cbiAgICBjb25zdCBzY2hvb2xzRGF0YSA9IGF3YWl0IGZldGNoVXNlclNjaG9vbHModXNlQ2FjaGUpO1xuICAgIHNldFN0YXRlKHtcbiAgICAgIHNjaG9vbHM6IHNjaG9vbHNEYXRhLFxuICAgICAgbG9hZGluZzogZmFsc2VcbiAgICB9KTtcbiAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCfojrflj5blrabmoKHliJfooajlpLHotKU6JywgZXJyKTtcbiAgICBzZXRTdGF0ZSh7XG4gICAgICBlcnJvcjogJ+iOt+WPluWtpuagoeWIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlScsXG4gICAgICBsb2FkaW5nOiBmYWxzZVxuICAgIH0pO1xuICB9XG59O1xuXG5cblxuLyoqXG4gKiDlpITnkIblrabmoKHpgInmi6nmqKHmgIHmoYbnmoTph43or5XpgLvovpFcbiAqIEBwYXJhbSBzZXRTdGF0ZSDnirbmgIHmm7TmlrDlh73mlbBcbiAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAqL1xuZXhwb3J0IGNvbnN0IGhhbmRsZVNjaG9vbE1vZGFsUmV0cnkgPSBhc3luYyAoXG4gIHNldFN0YXRlOiAoc3RhdGU6IFBhcnRpYWw8U2Nob29sTW9kYWxTdGF0ZT4pID0+IHZvaWQsXG4gIHVzZXJJZDogbnVtYmVyIHwgbnVsbFxuKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gIGF3YWl0IGhhbmRsZVNjaG9vbE1vZGFsRGF0YUxvYWQodXNlcklkLCBzZXRTdGF0ZSwgZmFsc2UpO1xufTtcblxuLyoqXG4gKiDlrabnlJ/mjqXlj6PlrprkuYlcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBTdHVkZW50IHtcbiAgaWQ6IG51bWJlcjtcbiAgdXNlcklkOiBudW1iZXI7XG4gIHN0dWRlbnROdW1iZXI6IHN0cmluZztcbiAgbmlja05hbWU6IHN0cmluZztcbiAgYXZhdGFyVXJsPzogc3RyaW5nO1xuICBjbGFzc0lkOiBudW1iZXI7XG59XG5cbi8qKlxuICog6I635Y+W54+t57qn5a2m55Sf5YiX6KGo77yI5bim6YCa55+l77yJXG4gKiBAcGFyYW0gY2xhc3NJZCDnj63nuqdJRFxuICogQHBhcmFtIG5vdGlmaWNhdGlvbiDpgJrnn6Xnu4Tku7ZcbiAqIEByZXR1cm5zIFByb21pc2U8U3R1ZGVudFtdPlxuICovXG5leHBvcnQgY29uc3QgZmV0Y2hDbGFzc1N0dWRlbnRzV2l0aE5vdGlmaWNhdGlvbiA9IGFzeW5jIChcbiAgY2xhc3NJZDogbnVtYmVyLFxuICBub3RpZmljYXRpb246IGFueVxuKTogUHJvbWlzZTxTdHVkZW50W10+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLmdldENsYXNzU3R1ZGVudHMoY2xhc3NJZCk7XG5cbiAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGEgfHwgW107XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWtpueUn+WIl+ihqOWksei0pTonLCByZXNwb25zZS5kYXRhLm1lc3NhZ2UpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfojrflj5blrabnlJ/liJfooajlpLHotKUnKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign6I635Y+W5a2m55Sf5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcbiAgICBub3RpZmljYXRpb24uZXJyb3IoJ+iOt+WPluWtpueUn+WIl+ihqOWksei0pScpO1xuICAgIHJldHVybiBbXTtcbiAgfVxufTtcblxuLyoqXG4gKiDojrflj5blrabnlJ/og73ph4/kv6Hmga/vvIjkvJjljJbniYjmnKzvvIlcbiAqIEBwYXJhbSB1c2VySWRzIOeUqOaIt0lE5pWw57uEXG4gKiBAcGFyYW0gbm90aWZpY2F0aW9uIOmAmuefpee7hOS7tlxuICogQHJldHVybnMgUHJvbWlzZTxNYXA8bnVtYmVyLCBudW1iZXI+PlxuICovXG5leHBvcnQgY29uc3QgZmV0Y2hTdHVkZW50UG9pbnRzID0gYXN5bmMgKFxuICB1c2VySWRzOiBudW1iZXJbXSxcbiAgbm90aWZpY2F0aW9uOiBhbnlcbik6IFByb21pc2U8TWFwPG51bWJlciwgbnVtYmVyPj4gPT4ge1xuICBpZiAodXNlcklkcy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gbmV3IE1hcCgpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCB7IHBvaW50c0FwaSB9ID0gYXdhaXQgaW1wb3J0KCdAL2xpYi9hcGkvcG9pbnRzJyk7XG4gICAgY29uc29sZS5sb2coXCLlvIDlp4vojrflj5blrabnlJ/og73ph4/kv6Hmga/vvIx1c2VySWRzOlwiLCB1c2VySWRzKTtcbiAgICBjb25zb2xlLmxvZyhcIuS9v+eUqEFQSeerr+eCuTpcIiwgYCR7cG9pbnRzQXBpLmJhc2VQb2ludFVybH0vYmF0Y2gtdG90YWxgKTtcblxuICAgIC8vIOiuvue9rjPnp5LotoXml7bvvIzpgb/lhY3plb/ml7bpl7TnrYnlvoXlvbHlk43nlKjmiLfkvZPpqoxcbiAgICBjb25zdCB0aW1lb3V0UHJvbWlzZSA9IG5ldyBQcm9taXNlKChfLCByZWplY3QpID0+IHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcign6K+35rGC6LaF5pe2JykpLCAzMDAwKTtcbiAgICB9KTtcblxuICAgIGNvbnN0IGFwaVByb21pc2UgPSBwb2ludHNBcGkuZ2V0QmF0Y2hTdHVkZW50UG9pbnRzKHVzZXJJZHMpO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgUHJvbWlzZS5yYWNlKFthcGlQcm9taXNlLCB0aW1lb3V0UHJvbWlzZV0pIGFzIGFueTtcblxuICAgIGNvbnN0IHBvaW50c01hcCA9IG5ldyBNYXA8bnVtYmVyLCBudW1iZXI+KCk7XG4gICAgY29uc29sZS5sb2coXCLmibnph4/np6/liIblk43lupRcIiwgcmVzcG9uc2UpO1xuXG4gICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xuICAgICAgLy8g6YGN5Y6G6L+U5Zue55qE5a2m55Sf56ev5YiG5pWw5o2uXG4gICAgICBmb3IgKGNvbnN0IHVzZXJJZCBpbiBkYXRhKSB7XG4gICAgICAgIGNvbnN0IHN0dWRlbnREYXRhID0gZGF0YVt1c2VySWRdO1xuICAgICAgICBpZiAoc3R1ZGVudERhdGEpIHtcbiAgICAgICAgICBjb25zdCB0b3RhbFBvaW50cyA9IHN0dWRlbnREYXRhLnRvdGFsUG9pbnRzIHx8IDA7XG4gICAgICAgICAgY29uc3QgYXZhaWxhYmxlUG9pbnRzID0gc3R1ZGVudERhdGEuYXZhaWxhYmxlUG9pbnRzIHx8IDA7XG4gICAgICAgICAgLy8g5L+u5q2j6K6h566X6YC76L6R77ya5LiOIGFzc2lnbi1wb2ludHMtbW9kYWwudHN4IOS/neaMgeS4gOiHtFxuICAgICAgICAgIC8vIHRvdGFsUG9pbnRzOiDmgLvnp6/liIYsIGF2YWlsYWJsZVBvaW50czog5bey5L2/55So56ev5YiGXG4gICAgICAgICAgLy8g5Y+v5YiG6YWN56ev5YiGID0g5oC756ev5YiGIC0g5bey5L2/55So56ev5YiGXG4gICAgICAgICAgY29uc3QgcmVtYWluaW5nUG9pbnRzID0gdG90YWxQb2ludHMgLSBhdmFpbGFibGVQb2ludHM7XG4gICAgICAgICAgcG9pbnRzTWFwLnNldChOdW1iZXIodXNlcklkKSwgTWF0aC5tYXgoMCwgcmVtYWluaW5nUG9pbnRzKSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcG9pbnRzTWFwLnNldChOdW1iZXIodXNlcklkKSwgMCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g56Gu5L+d5omA5pyJ6K+35rGC55qE55So5oi36YO95pyJ5pWw5o2uXG4gICAgICB1c2VySWRzLmZvckVhY2godWlkID0+IHtcbiAgICAgICAgaWYgKCFwb2ludHNNYXAuaGFzKHVpZCkpIHtcbiAgICAgICAgICBwb2ludHNNYXAuc2V0KHVpZCwgMCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcG9pbnRzTWFwO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBBUEnov5Tlm57plJnor6/vvIzorr7nva7pu5jorqTlgLzkvYbkuI3mmL7npLrplJnor6/mj5DnpLrvvIjpgb/lhY3lubLmibDnlKjmiLfkvZPpqozvvIlcbiAgICAgIHVzZXJJZHMuZm9yRWFjaCh1aWQgPT4gcG9pbnRzTWFwLnNldCh1aWQsIDApKTtcbiAgICAgIGNvbnNvbGUud2Fybign6I635Y+W5a2m55Sf6IO96YeP5L+h5oGv5aSx6LSl77yM5L2/55So6buY6K6k5YC8OicsIHJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XG4gICAgICByZXR1cm4gcG9pbnRzTWFwO1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+iOt+WPluWtpueUn+iDvemHj+Wksei0pTonLCBlcnJvcik7XG5cbiAgICAvLyDorr7nva7pu5jorqTlgLzvvIzorqnnlKjmiLflj6/ku6Xnu6fnu63mk43kvZxcbiAgICBjb25zdCBwb2ludHNNYXAgPSBuZXcgTWFwPG51bWJlciwgbnVtYmVyPigpO1xuICAgIHVzZXJJZHMuZm9yRWFjaCh1aWQgPT4gcG9pbnRzTWFwLnNldCh1aWQsIDApKTtcblxuICAgIC8vIOagueaNrumUmeivr+exu+Wei+WGs+WumuaYr+WQpuaYvuekuuaPkOekulxuICAgIGlmIChlcnJvci5tZXNzYWdlID09PSAn6K+35rGC6LaF5pe2Jykge1xuICAgICAgY29uc29sZS53YXJuKCfojrflj5bog73ph4/kv6Hmga/otoXml7bvvIzkvb/nlKjpu5jorqTlgLzvvIznlKjmiLflj6/nu6fnu63mk43kvZwnKTtcbiAgICAgIC8vIOS4jeaYvuekuumUmeivr+aPkOekuu+8jOmBv+WFjeW5suaJsOeUqOaIt+S9k+mqjFxuICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA+PSA1MDApIHtcbiAgICAgIGNvbnNvbGUud2Fybign5pyN5Yqh5Zmo6ZSZ6K+v77yM5L2/55So6buY6K6k6IO96YeP5YC8Jyk7XG4gICAgICAvLyDmnI3liqHlmajplJnor6/ml7bkuZ/kuI3mmL7npLrmj5DnpLrvvIzorqnnlKjmiLflj6/ku6Xnu6fnu63mk43kvZxcbiAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMykge1xuICAgICAgLy8g5p2D6ZmQ6Zeu6aKY5Y+v6IO96ZyA6KaB55So5oi355+l6YGTXG4gICAgICBub3RpZmljYXRpb24ud2FybmluZygn5pqC5peg5rOV6I635Y+W6IO96YeP5L+h5oGv77yM5bey6K6+572u6buY6K6k5YC8Jyk7XG4gICAgfVxuICAgIC8vIOWFtuS7lumUmeivr+S5n+S4jeaYvuekuuaPkOekuu+8jOS/neaMgeiJr+WlveeahOeUqOaIt+S9k+mqjFxuXG4gICAgcmV0dXJuIHBvaW50c01hcDtcbiAgfVxufTtcblxuLyoqXG4gKiDorqHnrpfmiYDmnInlrabnlJ/nmoTmnIDkvY7lj6/liIbphY3og73ph49cbiAqIEBwYXJhbSBzZWxlY3RlZFN0dWRlbnRzIOmAieS4reeahOWtpueUn0lE5pWw57uEXG4gKiBAcGFyYW0gc3R1ZGVudFBvaW50c01hcCDlrabnlJ/og73ph4/mmKDlsIRcbiAqIEByZXR1cm5zIOacgOS9juWPr+WIhumFjeiDvemHj1xuICovXG5leHBvcnQgY29uc3QgZ2V0TWluQXZhaWxhYmxlUG9pbnRzID0gKFxuICBzZWxlY3RlZFN0dWRlbnRzOiBudW1iZXJbXSxcbiAgc3R1ZGVudFBvaW50c01hcDogTWFwPG51bWJlciwgbnVtYmVyPlxuKTogbnVtYmVyID0+IHtcbiAgaWYgKHNlbGVjdGVkU3R1ZGVudHMubGVuZ3RoID09PSAwKSByZXR1cm4gMDtcblxuICBjb25zdCBzZWxlY3RlZFN0dWRlbnRQb2ludHMgPSBzZWxlY3RlZFN0dWRlbnRzLm1hcChzdHVkZW50SWQgPT5cbiAgICBzdHVkZW50UG9pbnRzTWFwLmdldChzdHVkZW50SWQpIHx8IDBcbiAgKTtcblxuICByZXR1cm4gTWF0aC5taW4oLi4uc2VsZWN0ZWRTdHVkZW50UG9pbnRzKTtcbn07XG5cbi8qKlxuICog6I635Y+W5b2T5YmN5YiG6YWN5pa55byP55qE5o+Q56S65L+h5oGvXG4gKiBAcGFyYW0gc2VsZWN0ZWREaXN0cmlidXRpb24g6YCJ5Lit55qE5YiG6YWN5pa55byPXG4gKiBAcGFyYW0gc2VsZWN0ZWRTdHVkZW50cyDpgInkuK3nmoTlrabnlJ9JROaVsOe7hFxuICogQHBhcmFtIHN0dWRlbnRQb2ludHNNYXAg5a2m55Sf6IO96YeP5pig5bCEXG4gKiBAcmV0dXJucyDmj5DnpLrkv6Hmga/lr7nosaFcbiAqL1xuZXhwb3J0IGNvbnN0IGdldEVuZXJneURpc3BsYXlJbmZvID0gKFxuICBzZWxlY3RlZERpc3RyaWJ1dGlvbjogc3RyaW5nLFxuICBzZWxlY3RlZFN0dWRlbnRzOiBudW1iZXJbXSxcbiAgc3R1ZGVudFBvaW50c01hcDogTWFwPG51bWJlciwgbnVtYmVyPlxuKTogeyBsYWJlbDogc3RyaW5nOyB2YWx1ZTogbnVtYmVyIH0gPT4ge1xuICBpZiAoc2VsZWN0ZWREaXN0cmlidXRpb24gPT09ICdhc3NpZ24nIHx8IHNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcbiAgICByZXR1cm4ge1xuICAgICAgbGFiZWw6ICfmnIDkvY7lj6/liIbphY3og73ph48nLFxuICAgICAgdmFsdWU6IGdldE1pbkF2YWlsYWJsZVBvaW50cyhzZWxlY3RlZFN0dWRlbnRzLCBzdHVkZW50UG9pbnRzTWFwKVxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHsgbGFiZWw6ICcnLCB2YWx1ZTogMCB9O1xufTtcbiJdLCJuYW1lcyI6WyJHZXROb3RpZmljYXRpb24iLCJNb2RhbCIsImNsYXNzQXBpIiwic3R1ZGVudEFwaSIsInNjaG9vbEFwaSIsInVwZGF0ZUNsYXNzSW5mbyIsImNsYXNzSW5mbyIsInZhbHVlcyIsIm5vdGlmaWNhdGlvbiIsInJlc3BvbnNlIiwidXBkYXRlQ2xhc3NTaW1wbGUiLCJpZCIsImNsYXNzTmFtZSIsImRhdGEiLCJjb2RlIiwic3VjY2VzcyIsImVycm9yIiwibWVzc2FnZSIsImNvbnNvbGUiLCJkZWxldGVDbGFzcyIsImNsYXNzSWQiLCJ0cmFuc2ZlckNsYXNzIiwibmV3VGVhY2hlcklkIiwidHJhbnNmZXJUeXBlIiwiZ2VuZXJhdGVDbGFzc0ludml0ZUNvZGUiLCJnZW5lcmF0ZUludml0ZUNvZGUiLCJpbnZpdGVDb2RlIiwic2VhcmNoVGVhY2hlckJ5UGhvbmUiLCJwaG9uZSIsImV4cG9ydFN0dWRlbnRzRGF0YSIsInN0dWRlbnRzIiwibGVuZ3RoIiwid2FybmluZyIsImV4cG9ydERhdGEiLCJtYXAiLCJzdHVkZW50IiwiaW5kZXgiLCLluo/lj7ciLCLlrablj7ciLCJzdHVkZW50TnVtYmVyIiwi5aeT5ZCNIiwibmlja05hbWUiLCLmgLvnp6/liIYiLCJ0b3RhbFBvaW50cyIsIuWPr+eUqOenr+WIhiIsImF2YWlsYWJsZVBvaW50cyIsIuW9k+WJjeaooeadvyIsImN1cnJlbnRUZW1wbGF0ZSIsInRlbXBsYXRlTmFtZSIsImhlYWRlcnMiLCJPYmplY3QiLCJrZXlzIiwiY3N2Q29udGVudCIsImpvaW4iLCJyb3ciLCJoZWFkZXIiLCJibG9iIiwiQmxvYiIsInR5cGUiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwidXJsIiwiVVJMIiwiY3JlYXRlT2JqZWN0VVJMIiwic2V0QXR0cmlidXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJzdHlsZSIsInZpc2liaWxpdHkiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwiZXhwb3J0U3R1ZGVudHNWaWFBUEkiLCJoaWRlTG9hZGluZyIsImxvYWRpbmciLCJleHBvcnRTdHVkZW50cyIsImNsb3NlIiwidmFsaWRhdGVDbGFzc05hbWUiLCJ0cmltIiwiaXNWYWxpZCIsImludmFsaWRDaGFycyIsInRlc3QiLCJlZGl0Q2xhc3NJbmZvIiwibG9nIiwidXBkYXRlQ2xhc3MiLCJncmFkZSIsInN0YXR1cyIsInJlbW92ZUFzc2lzdGFudFRlYWNoZXIiLCJhc3Npc3RhbnRUZWFjaGVySWQiLCJkZWxldGVDbGFzc1dpdGhDaGVjayIsIm9uU3VjY2VzcyIsInRpdGxlIiwiY29udGVudCIsIm9rVGV4dCIsIlByb21pc2UiLCJyZXNvbHZlIiwiY29uZmlybSIsIm9rVHlwZSIsImNhbmNlbFRleHQiLCJjZW50ZXJlZCIsIm9uT2siLCJvbkNhbmNlbCIsInNjaG9vbHNDYWNoZSIsIlNDSE9PTF9DQUNIRV9EVVJBVElPTiIsImZldGNoVXNlclNjaG9vbHMiLCJ1c2VDYWNoZSIsIm5vdyIsInRpbWVzdGFtcCIsInVzZXJJbmZvIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkVycm9yIiwidXNlciIsIkpTT04iLCJwYXJzZSIsInVzZXJJZCIsInRlYWNoZXJJZCIsImdldFVzZXJTY2hvb2xzIiwic2Nob29sc0RhdGEiLCJBcnJheSIsImlzQXJyYXkiLCJtc2ciLCJjbGVhclNjaG9vbHNDYWNoZSIsImdldFNjaG9vbHNDYWNoZUluZm8iLCJpc0V4cGlyZWQiLCJkYXRhQ291bnQiLCJyZW1haW5pbmdUaW1lIiwicHJlbG9hZFNjaG9vbHNEYXRhIiwiZGVsYXkiLCJzZXRUaW1lb3V0IiwiVEVBQ0hFUl9DQUNIRV9EVVJBVElPTiIsInRlYWNoZXJDbGFzc2VzQ2FjaGUiLCJkZWJ1Z0NsYXNzQ2FjaGUiLCJmb3JFYWNoIiwic2Nob29sSWQiLCJjYWNoZSIsIk51bWJlciIsIuePree6p+aVsOmHjyIsIue8k+WtmOaXtumXtCIsIuaYr+WQpui/h+acnyIsIuWJqeS9meaXtumXtCIsIk1hdGgiLCJyb3VuZCIsImdldFRlYWNoZXJDbGFzc0NhY2hlSW5mbyIsImhhbmRsZVNjaG9vbFNlbGVjdGlvbiIsInNjaG9vbCIsImNhbGxiYWNrcyIsInNjaG9vbE5hbWUiLCJvblNjaG9vbFNlbGVjdCIsIm9uTWVudUl0ZW1DbGljayIsIm9uQ2xhc3Nlc1VwZGF0ZSIsImNsYXNzTGlzdCIsImZldGNoVGVhY2hlckNsYXNzZXMiLCJpbml0U2Nob29sTW9kYWxTdGF0ZSIsInNjaG9vbHMiLCJtb3VudGVkIiwiaGFuZGxlU2Nob29sTW9kYWxEYXRhTG9hZCIsInNldFN0YXRlIiwiZXJyIiwiaGFuZGxlU2Nob29sTW9kYWxSZXRyeSIsImZldGNoQ2xhc3NTdHVkZW50c1dpdGhOb3RpZmljYXRpb24iLCJnZXRDbGFzc1N0dWRlbnRzIiwiZmV0Y2hTdHVkZW50UG9pbnRzIiwidXNlcklkcyIsIk1hcCIsInBvaW50c0FwaSIsImJhc2VQb2ludFVybCIsInRpbWVvdXRQcm9taXNlIiwiXyIsInJlamVjdCIsImFwaVByb21pc2UiLCJnZXRCYXRjaFN0dWRlbnRQb2ludHMiLCJyYWNlIiwicG9pbnRzTWFwIiwic3R1ZGVudERhdGEiLCJyZW1haW5pbmdQb2ludHMiLCJzZXQiLCJtYXgiLCJ1aWQiLCJoYXMiLCJ3YXJuIiwiZ2V0TWluQXZhaWxhYmxlUG9pbnRzIiwic2VsZWN0ZWRTdHVkZW50cyIsInN0dWRlbnRQb2ludHNNYXAiLCJzZWxlY3RlZFN0dWRlbnRQb2ludHMiLCJzdHVkZW50SWQiLCJnZXQiLCJtaW4iLCJnZXRFbmVyZ3lEaXNwbGF5SW5mbyIsInNlbGVjdGVkRGlzdHJpYnV0aW9uIiwibGFiZWwiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/classUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/utils/index.ts":
/*!**************************************!*\
  !*** ./app/workbench/utils/index.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetNotification: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.GetNotification; },\n/* harmony export */   addStudentToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.addStudentToClass; },\n/* harmony export */   applyTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.applyTemplate; },\n/* harmony export */   assignPointsToStudent: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.assignPointsToStudent; },\n/* harmony export */   assignTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.assignTemplateToStudents; },\n/* harmony export */   batchAssignPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchAssignPoints; },\n/* harmony export */   batchUseKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.batchUseKeys; },\n/* harmony export */   calculateCompletionRate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateCompletionRate; },\n/* harmony export */   calculateQuickTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.calculateQuickTime; },\n/* harmony export */   calculateTotalAvailablePoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.calculateTotalAvailablePoints; },\n/* harmony export */   clearSchoolsCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.clearSchoolsCache; },\n/* harmony export */   debounce: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.debounce; },\n/* harmony export */   debugClassCache: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.debugClassCache; },\n/* harmony export */   deepClone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.deepClone; },\n/* harmony export */   deleteClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClass; },\n/* harmony export */   deleteClassWithCheck: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.deleteClassWithCheck; },\n/* harmony export */   editClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.editClassInfo; },\n/* harmony export */   exportStudentsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsData; },\n/* harmony export */   exportStudentsViaAPI: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.exportStudentsViaAPI; },\n/* harmony export */   fetchAllClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchAllClassProjects; },\n/* harmony export */   fetchBatchStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchBatchStudentPoints; },\n/* harmony export */   fetchClassProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchClassProjects; },\n/* harmony export */   fetchClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchClassStudents; },\n/* harmony export */   fetchClassStudentsWithNotification: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchClassStudentsWithNotification; },\n/* harmony export */   fetchClassTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchClassTasks; },\n/* harmony export */   fetchCompleteClassStudents: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchCompleteClassStudents; },\n/* harmony export */   fetchCurrentTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchCurrentTemplate; },\n/* harmony export */   fetchFolderTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolderTemplates; },\n/* harmony export */   fetchFolders: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchFolders; },\n/* harmony export */   fetchKeyRecords: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.fetchKeyRecords; },\n/* harmony export */   fetchMyTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchMyTemplates; },\n/* harmony export */   fetchOfficialTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchOfficialTemplates; },\n/* harmony export */   fetchSingleStudentPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.fetchSingleStudentPoints; },\n/* harmony export */   fetchSpecialTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.fetchSpecialTemplate; },\n/* harmony export */   fetchStudentPoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchStudentPoints; },\n/* harmony export */   fetchStudentsInfo: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.fetchStudentsInfo; },\n/* harmony export */   fetchTeacherClassesForProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fetchTeacherClassesForProjects; },\n/* harmony export */   fetchTeacherWorks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.fetchTeacherWorks; },\n/* harmony export */   fetchUserSchools: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.fetchUserSchools; },\n/* harmony export */   fetchWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fetchWorks; },\n/* harmony export */   filterTasks: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.filterTasks; },\n/* harmony export */   filterTemplates: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.filterTemplates; },\n/* harmony export */   fixImageUrl: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.fixImageUrl; },\n/* harmony export */   fixProjectImageUrl: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.fixProjectImageUrl; },\n/* harmony export */   formatAddressDisplay: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatAddressDisplay; },\n/* harmony export */   formatDateTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatDateTime; },\n/* harmony export */   formatFileSize: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.formatFileSize; },\n/* harmony export */   formatPackageInfo: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.formatPackageInfo; },\n/* harmony export */   formatPoints: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.formatPoints; },\n/* harmony export */   formatPublishTime: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.formatPublishTime; },\n/* harmony export */   formatTaskDate: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDate; },\n/* harmony export */   formatTaskDateSimple: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTaskDateSimple; },\n/* harmony export */   formatTimeDisplay: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.formatTimeDisplay; },\n/* harmony export */   generateClassInviteCode: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.generateClassInviteCode; },\n/* harmony export */   generateRandomString: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateRandomString; },\n/* harmony export */   generateStepIndicatorConfig: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.generateStepIndicatorConfig; },\n/* harmony export */   getAvatarColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarColor; },\n/* harmony export */   getAvatarGradientColor: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.getAvatarGradientColor; },\n/* harmony export */   getBatchStudentPackageExpiries: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getBatchStudentPackageExpiries; },\n/* harmony export */   getCurrentDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getCurrentDateTime; },\n/* harmony export */   getCurrentEnergyAmount: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getCurrentEnergyAmount; },\n/* harmony export */   getCurrentUserId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserId; },\n/* harmony export */   getCurrentUserSchoolId: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.getCurrentUserSchoolId; },\n/* harmony export */   getEnergyDisplayInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getEnergyDisplayInfo; },\n/* harmony export */   getMinAvailablePoints: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getMinAvailablePoints; },\n/* harmony export */   getNextDayDateTime: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getNextDayDateTime; },\n/* harmony export */   getRelativeTime: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.getRelativeTime; },\n/* harmony export */   getSchoolsCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getSchoolsCacheInfo; },\n/* harmony export */   getStudentPackageExpiry: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.getStudentPackageExpiry; },\n/* harmony export */   getTaskRemainingTime: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskRemainingTime; },\n/* harmony export */   getTaskStatus: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatus; },\n/* harmony export */   getTaskStatusColor: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.getTaskStatusColor; },\n/* harmony export */   getTaskTimes: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTaskTimes; },\n/* harmony export */   getTeacherClassCacheInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.getTeacherClassCacheInfo; },\n/* harmony export */   getTemplateDetails: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.getTemplateDetails; },\n/* harmony export */   handleDeleteTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleDeleteTask; },\n/* harmony export */   handleEditTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleEditTask; },\n/* harmony export */   handleMouseDown: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseDown; },\n/* harmony export */   handleMouseLeave: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseLeave; },\n/* harmony export */   handleMouseMove: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseMove; },\n/* harmony export */   handleMouseUp: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleMouseUp; },\n/* harmony export */   handleSchoolModalDataLoad: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalDataLoad; },\n/* harmony export */   handleSchoolModalRetry: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolModalRetry; },\n/* harmony export */   handleSchoolSelection: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.handleSchoolSelection; },\n/* harmony export */   handleSelectWork: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleSelectWork; },\n/* harmony export */   handleViewTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.handleViewTask; },\n/* harmony export */   handleViewWork: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.handleViewWork; },\n/* harmony export */   handleWheelScroll: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.handleWheelScroll; },\n/* harmony export */   importStudentsFromFile: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsFromFile; },\n/* harmony export */   importStudentsToClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.importStudentsToClass; },\n/* harmony export */   initClassProjectsState: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.initClassProjectsState; },\n/* harmony export */   initDragState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initDragState; },\n/* harmony export */   initSchoolModalState: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.initSchoolModalState; },\n/* harmony export */   initTemplateModalData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateModalData; },\n/* harmony export */   initTemplateModalState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplateModalState; },\n/* harmony export */   initTemplatePickerState: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.initTemplatePickerState; },\n/* harmony export */   initTemplateTaskData: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.initTemplateTaskData; },\n/* harmony export */   initWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.initWorksState; },\n/* harmony export */   isValidEmail: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidEmail; },\n/* harmony export */   isValidKeyFormat: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.isValidKeyFormat; },\n/* harmony export */   isValidPhone: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.isValidPhone; },\n/* harmony export */   loadMoreWorks: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.loadMoreWorks; },\n/* harmony export */   mergeStudentData: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.mergeStudentData; },\n/* harmony export */   parseExcelKeys: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.parseExcelKeys; },\n/* harmony export */   preloadSchoolsData: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.preloadSchoolsData; },\n/* harmony export */   prepareBlockAssignment: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.prepareBlockAssignment; },\n/* harmony export */   publishTask: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.publishTask; },\n/* harmony export */   redeemKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.redeemKey; },\n/* harmony export */   removeAssistantTeacher: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.removeAssistantTeacher; },\n/* harmony export */   removeFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.removeFile; },\n/* harmony export */   removeStudentsFromClass: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.removeStudentsFromClass; },\n/* harmony export */   removeTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.removeTemplate; },\n/* harmony export */   resetStudentPassword: function() { return /* reexport safe */ _studentUtils__WEBPACK_IMPORTED_MODULE_1__.resetStudentPassword; },\n/* harmony export */   resetWorksState: function() { return /* reexport safe */ _worksUtils__WEBPACK_IMPORTED_MODULE_8__.resetWorksState; },\n/* harmony export */   searchTeacherByPhone: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.searchTeacherByPhone; },\n/* harmony export */   shouldShowEmptyState: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.shouldShowEmptyState; },\n/* harmony export */   showBatchOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showBatchOperationResult; },\n/* harmony export */   showConfirm: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showConfirm; },\n/* harmony export */   showError: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showError; },\n/* harmony export */   showInfo: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showInfo; },\n/* harmony export */   showLoading: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showLoading; },\n/* harmony export */   showOperationResult: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showOperationResult; },\n/* harmony export */   showRedeemConfirmModal: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.showRedeemConfirmModal; },\n/* harmony export */   showSuccess: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showSuccess; },\n/* harmony export */   showWarning: function() { return /* reexport safe */ _notificationUtils__WEBPACK_IMPORTED_MODULE_5__.showWarning; },\n/* harmony export */   sortProjects: function() { return /* reexport safe */ _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__.sortProjects; },\n/* harmony export */   syncTeacherTemplateToStudents: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.syncTeacherTemplateToStudents; },\n/* harmony export */   throttle: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.throttle; },\n/* harmony export */   transferClass: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.transferClass; },\n/* harmony export */   triggerPointsUpdateEvent: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.triggerPointsUpdateEvent; },\n/* harmony export */   uniqueArray: function() { return /* reexport safe */ _baseUtils__WEBPACK_IMPORTED_MODULE_0__.uniqueArray; },\n/* harmony export */   updateClassInfo: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.updateClassInfo; },\n/* harmony export */   updatePersonalTemplateAssignments: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updatePersonalTemplateAssignments; },\n/* harmony export */   updateStudentsTemplate: function() { return /* reexport safe */ _templateUtils__WEBPACK_IMPORTED_MODULE_2__.updateStudentsTemplate; },\n/* harmony export */   uploadFile: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.uploadFile; },\n/* harmony export */   validateClassName: function() { return /* reexport safe */ _classUtils__WEBPACK_IMPORTED_MODULE_4__.validateClassName; },\n/* harmony export */   validateEnergyDistribution: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateEnergyDistribution; },\n/* harmony export */   validateExcelFile: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validateExcelFile; },\n/* harmony export */   validateKey: function() { return /* reexport safe */ _keyUtils__WEBPACK_IMPORTED_MODULE_7__.validateKey; },\n/* harmony export */   validatePointsAssignment: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignment; },\n/* harmony export */   validatePointsAssignmentModal: function() { return /* reexport safe */ _pointsUtils__WEBPACK_IMPORTED_MODULE_3__.validatePointsAssignmentModal; },\n/* harmony export */   validateTaskForm: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskForm; },\n/* harmony export */   validateTaskParams: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateTaskParams; },\n/* harmony export */   validateUploadFiles: function() { return /* reexport safe */ _taskUtils__WEBPACK_IMPORTED_MODULE_6__.validateUploadFiles; }\n/* harmony export */ });\n/* harmony import */ var _baseUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baseUtils */ \"(app-pages-browser)/./app/workbench/utils/baseUtils.ts\");\n/* harmony import */ var _studentUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./studentUtils */ \"(app-pages-browser)/./app/workbench/utils/studentUtils.ts\");\n/* harmony import */ var _templateUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templateUtils */ \"(app-pages-browser)/./app/workbench/utils/templateUtils.ts\");\n/* harmony import */ var _pointsUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointsUtils */ \"(app-pages-browser)/./app/workbench/utils/pointsUtils.ts\");\n/* harmony import */ var _classUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./classUtils */ \"(app-pages-browser)/./app/workbench/utils/classUtils.ts\");\n/* harmony import */ var _notificationUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notificationUtils */ \"(app-pages-browser)/./app/workbench/utils/notificationUtils.ts\");\n/* harmony import */ var _taskUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./taskUtils */ \"(app-pages-browser)/./app/workbench/utils/taskUtils.ts\");\n/* harmony import */ var _keyUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./keyUtils */ \"(app-pages-browser)/./app/workbench/utils/keyUtils.ts\");\n/* harmony import */ var _worksUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./worksUtils */ \"(app-pages-browser)/./app/workbench/utils/worksUtils.ts\");\n/* harmony import */ var _classProjectsUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./classProjectsUtils */ \"(app-pages-browser)/./app/workbench/utils/classProjectsUtils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./app/workbench/utils/types.ts\");\n// 工作台通用工具函数导出\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvdXRpbHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxjQUFjO0FBQ2M7QUFDRztBQUNDO0FBQ0Y7QUFDRDtBQUNPO0FBQ1I7QUFDRDtBQUNFO0FBQ1E7QUFRYiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvd29ya2JlbmNoL3V0aWxzL2luZGV4LnRzP2I2MTAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g5bel5L2c5Y+w6YCa55So5bel5YW35Ye95pWw5a+85Ye6XG5leHBvcnQgKiBmcm9tICcuL2Jhc2VVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3N0dWRlbnRVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3RlbXBsYXRlVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9wb2ludHNVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL2NsYXNzVXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi9ub3RpZmljYXRpb25VdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL3Rhc2tVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL2tleVV0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vd29ya3NVdGlscyc7XG5leHBvcnQgKiBmcm9tICcuL2NsYXNzUHJvamVjdHNVdGlscyc7XG5cbi8vIOWvvOWHuuaooeadv+euoeeQhuebuOWFs+exu+Wei1xuZXhwb3J0IHR5cGUgeyBUZW1wbGF0ZSwgRm9sZGVyLCBUZW1wbGF0ZU1vZGFsU3RhdGUsIFRlbXBsYXRlUGlja2VyU3RhdGUgfSBmcm9tICcuL3RlbXBsYXRlVXRpbHMnO1xuZXhwb3J0IHR5cGUgeyBTdHVkZW50IH0gZnJvbSAnLi9jbGFzc1V0aWxzJztcbmV4cG9ydCB0eXBlIHsgV29ya3NTdGF0ZSwgRHJhZ1N0YXRlIH0gZnJvbSAnLi93b3Jrc1V0aWxzJztcbmV4cG9ydCB0eXBlIHsgVGVtcGxhdGVUYXNrRGF0YSwgVGVtcGxhdGVNb2RhbERhdGEgfSBmcm9tICcuL3Rhc2tVdGlscyc7XG5leHBvcnQgdHlwZSB7IFByb2plY3QsIENsYXNzUHJvamVjdHNTdGF0ZSB9IGZyb20gJy4vY2xhc3NQcm9qZWN0c1V0aWxzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/utils/index.ts\n"));

/***/ })

});