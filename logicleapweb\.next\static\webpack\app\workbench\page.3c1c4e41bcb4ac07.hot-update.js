"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 移除本地缓存管理，使用工具函数中的缓存\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            loadClasses(firstSchool.id);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表\n        loadClasses(school.id);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表 - 使用工具函数\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            const schoolList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherSchools)();\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                loadClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(error.message || \"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用工具函数\n    const loadClasses = async function(schoolId) {\n        let useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            // 延迟重试，等待用户信息加载完成\n            setTimeout(()=>{\n                if (userInfo === null || userInfo === void 0 ? void 0 : userInfo.id) {\n                    console.log(\"用户信息已加载，重新尝试获取班级列表\");\n                    loadClasses(schoolId, useCache);\n                }\n            }, 1000);\n            return;\n        }\n        try {\n            // 通知父组件开始加载\n            onClassesUpdate && onClassesUpdate([], true, null);\n            console.log(\"开始获取班级列表，学校ID:\", schoolId, \"用户ID:\", userInfo.id);\n            const classList = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchTeacherClasses)(schoolId, userInfo.id, useCache);\n            // 同时更新ClassSelectionModal的缓存\n            (0,_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__.updateClassCache)(schoolId, classList);\n            // 通知父组件数据更新\n            onClassesUpdate && onClassesUpdate(classList, false, null);\n            console.log(\"成功获取班级列表，数量:\", classList.length);\n            // 如果班级列表为空，尝试不使用缓存重新获取\n            if (classList.length === 0 && useCache) {\n                console.log(\"班级列表为空，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 500);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = error.message || \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            // 如果使用缓存失败，尝试不使用缓存重新获取\n            if (useCache) {\n                console.log(\"使用缓存获取失败，尝试不使用缓存重新获取\");\n                setTimeout(()=>{\n                    loadClasses(schoolId, false);\n                }, 1000);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined),\n            userInfo.nickName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"login-prompt\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-icon-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-10 h-10 text-gray-400\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"login-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-text\",\n                                children: \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"login-subtitle\",\n                                children: \"请先登录以使用完整功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_3__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});